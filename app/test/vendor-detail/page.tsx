import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"

export default async function TestVendorDetailPage() {
  const supabase = await createClient()
  
  // Get the first vendor for testing
  const { data: vendors, error } = await supabase
    .from('vendors')
    .select('id, business_name')
    .limit(1)

  if (error || !vendors || vendors.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            No Vendors Found
          </h1>
          <p className="text-gray-600 mb-6">
            Please create a vendor account first to test the vendor detail page.
          </p>
          <a
            href="/vendor/onboarding"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Vendor Account
          </a>
        </div>
      </div>
    )
  }

  // Redirect to the vendor detail page
  redirect(`/vendor/${vendors[0].id}`)
}
