import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
      const { id } = await params
    // Check if Supa<PERSON> is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const coupleId = id;

    // Get couple profile
    const { data: profile, error: profileError } = await supabaseServer
      .from('profiles')
      .select('id, full_name, phone')
      .eq('id', coupleId)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({ error: 'Couple not found' }, { status: 404 });
    }

    // Get couple's wedding details
    const { data: wedding, error: weddingError } = await supabaseServer
      .from('weddings')
      .select(`
        *,
        venues (
          id,
          name,
          address,
          city,
          state,
          is_tented,
          latitude,
          longitude
        )
      `)
      .eq('couple_id', coupleId)
      .single();

      console.log('wedding', wedding)
    if (weddingError || !wedding) {
      return NextResponse.json({ error: 'Wedding details not found' }, { status: 404 });
    }

    // Calculate distance from current user's venue (simplified - you might want to get this from the match data)
    let distance = 0;
    try {
      // Get current user's venue for distance calculation
      const { data: userVenue } = await supabaseServer
        .from('venues')
        .select('latitude, longitude')
        .eq('created_by', user.id)
        .single();

      if (userVenue && wedding.venues?.latitude && wedding.venues?.longitude) {
        // Simple distance calculation (you might want to use a more accurate method)
        const lat1 = userVenue.latitude;
        const lon1 = userVenue.longitude;
        const lat2 = wedding.venues.latitude;
        const lon2 = wedding.venues.longitude;

        const R = 3959; // Earth's radius in miles
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        distance = R * c;
      }
    } catch (error) {
      console.error('Error calculating distance:', error);
    }

    // Get couple's photos (from storage or venue images)
    let photos: string[] = [];
    try {
      // You might want to implement photo fetching from your storage bucket
      // For now, using placeholder
      photos = ['/placeholder-wedding.jpg'];
    } catch (error) {
      console.error('Error fetching photos:', error);
    }

    // Prepare couple details
    const coupleDetails = {
      id: profile.id,
      name: profile.full_name,
      email: '', // We'll get this from auth if needed
      phone: profile.phone,
      weddingDate: wedding.date,
      venue: {
        id: wedding.venues?.id || '',
        name: wedding.venues?.name || '',
        address: wedding.venues?.address || '',
        city: wedding.venues?.city || '',
        state: wedding.venues?.state || '',
        isTented: wedding.venues?.is_tented || false,
      },
      photos,
      description: wedding.venues?.description,
      distance,
    };

    // Get tent packages if venue is tented (excluding already booked ones)
    let tentPackages: any[] = [];
    if (wedding.venues?.is_tented) {
      // First get all packages for this venue
      const { data: packages, error: packagesError } = await supabaseServer
        .from('tent_packages')
        .select('*')
        .eq('venue_id', wedding.venues.id);

      if (!packagesError && packages) {
        // Get booked packages for the wedding date range
        const weddingDate = new Date(wedding.date);
        const startDate = new Date(weddingDate);
        startDate.setDate(startDate.getDate() - 1); // Day before
        const endDate = new Date(weddingDate);
        endDate.setDate(endDate.getDate() + 1); // Day after

        const { data: bookedPackages } = await supabaseServer
          .from('tented_venue_bookings')
          .select('tent_package_id')
          .eq('venue_id', wedding.venues.id)
          .eq('status', 'confirmed')
          .or(`start_date.lte.${endDate.toISOString()},end_date.gte.${startDate.toISOString()}`);

        const bookedPackageIds = new Set(
          (bookedPackages || []).map(booking => booking.tent_package_id)
        );

        // Filter out booked packages
        tentPackages = packages
          .filter(pkg => !bookedPackageIds.has(pkg.id))
          .map(pkg => ({
            id: pkg.id,
            name: pkg.name,
            size: pkg.size,
            capacity: pkg.capacity,
            price: pkg.price,
            features: Array.isArray(pkg.features) ? pkg.features : [],
            venueId: pkg.venue_id,
            available: true,
          }));
      }
    }

    // Get products listed by this couple (including booked ones to show status)
    const { data: products, error: productsError } = await supabaseServer
      .from('products')
      .select('*')
      .eq('owner_id', coupleId)
      .eq('owner_type', 'couple')
      .in('status', ['available', 'booked', 'pending'])
      .order('created_at', { ascending: false });

    const coupleProducts = products ? products.map(product => ({
      id: product.id,
      title: product.title,
      description: product.description,
      reuse_price: product.reuse_price,
      original_price: product.original_price,
      type: product.type,
      status: product.status,
      location: product.location,
      date_available: product.date_available,
      tags: product.tags || [],
    })) : [];

    return NextResponse.json({
      couple: coupleDetails,
      tentPackages,
      products: coupleProducts,
    });

  } catch (error) {
    console.error('Error fetching couple details:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch couple details';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
