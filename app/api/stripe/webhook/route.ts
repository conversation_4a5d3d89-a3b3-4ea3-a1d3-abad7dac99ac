import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY || !process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('Stripe configuration missing');
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Get the raw body
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json({ error: 'No signature' }, { status: 400 });
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Create Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // Use service role for webhook
      {
        cookies: {
          getAll() { return []; },
          setAll() { },
        },
      }
    );

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        
        if (session.metadata?.type === 'tent_package_booking') {
          await handleTentBookingSuccess(session, supabase);
        } else if (session.metadata?.type === 'product_booking') {
          await handleProductBookingSuccess(session, supabase);
        }
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        
        if (failedPayment.metadata?.type === 'tent_package_booking') {
          await handleTentBookingFailure(failedPayment, supabase);
        } else if (failedPayment.metadata?.type === 'product_booking') {
          await handleProductBookingFailure(failedPayment, supabase);
        }
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook error' }, { status: 500 });
  }
}

async function handleTentBookingSuccess(session: Stripe.Checkout.Session, supabase: any) {
  try {
    const bookingId = session.metadata?.booking_id;
    
    if (!bookingId) {
      console.error('No booking ID in session metadata');
      return;
    }

    // Update booking status to confirmed
    const { error: bookingError } = await supabase
      .from('tented_venue_bookings')
      .update({ 
        status: 'confirmed',
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);

    if (bookingError) {
      console.error('Error updating booking:', bookingError);
      return;
    }

    // Update transaction status
    const { error: transactionError } = await supabase
      .from('transactions')
      .update({ 
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('resource_id', session.metadata?.tent_package_id)
      .eq('buyer_id', session.metadata?.buyer_id)
      .eq('status', 'pending');

    if (transactionError) {
      console.error('Error updating transaction:', transactionError);
    }

    console.log(`Tent booking ${bookingId} confirmed successfully`);

  } catch (error) {
    console.error('Error handling tent booking success:', error);
  }
}

async function handleProductBookingSuccess(session: Stripe.Checkout.Session, supabase: any) {
  try {
    const productId = session.metadata?.product_id;
    const buyerId = session.metadata?.buyer_id;
    const sellerId = session.metadata?.seller_id;
    
    if (!productId || !buyerId || !sellerId) {
      console.error('Missing metadata in product booking session');
      return;
    }

    // Update product status to booked
    const { error: productError } = await supabase
      .from('products')
      .update({ 
        status: 'booked',
        updated_at: new Date().toISOString()
      })
      .eq('id', productId);

    if (productError) {
      console.error('Error updating product status:', productError);
      return;
    }

    // Update transaction status
    const { error: transactionError } = await supabase
      .from('transactions')
      .update({ 
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('resource_id', productId)
      .eq('buyer_id', buyerId)
      .eq('status', 'pending');

    if (transactionError) {
      console.error('Error updating transaction:', transactionError);
    }

    // Create notification for the seller
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: sellerId,
        type: 'transaction',
        title: 'Product Sold',
        message: 'Your product has been purchased. Payment is held in escrow.',
        action_url: `/products/${productId}`,
        related_id: productId,
      });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
    }

    console.log(`Product ${productId} booking confirmed successfully`);

  } catch (error) {
    console.error('Error handling product booking success:', error);
  }
}

async function handleTentBookingFailure(paymentIntent: Stripe.PaymentIntent, supabase: any) {
  try {
    const bookingId = paymentIntent.metadata?.booking_id;
    
    if (!bookingId) {
      console.error('No booking ID in payment intent metadata');
      return;
    }

    // Update booking status to cancelled
    const { error: bookingError } = await supabase
      .from('tented_venue_bookings')
      .update({ 
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);

    if (bookingError) {
      console.error('Error updating failed booking:', bookingError);
      return;
    }

    console.log(`Tent booking ${bookingId} cancelled due to payment failure`);

  } catch (error) {
    console.error('Error handling tent booking failure:', error);
  }
}

async function handleProductBookingFailure(paymentIntent: Stripe.PaymentIntent, supabase: any) {
  try {
    const productId = paymentIntent.metadata?.product_id;
    
    if (!productId) {
      console.error('No product ID in payment intent metadata');
      return;
    }

    // Revert product status back to available
    const { error: productError } = await supabase
      .from('products')
      .update({ 
        status: 'available',
        updated_at: new Date().toISOString()
      })
      .eq('id', productId);

    if (productError) {
      console.error('Error reverting product status:', productError);
      return;
    }

    console.log(`Product ${productId} booking cancelled due to payment failure`);

  } catch (error) {
    console.error('Error handling product booking failure:', error);
  }
}
