import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { supabase } from '@/lib/supabase';
import { createServerClient } from '@supabase/ssr';

// Check if <PERSON><PERSON> is configured
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('STRIPE_SECRET_KEY is not configured');
}

const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-02-24.acacia',
}) : null;

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON>e is configured
    if (!stripe) {
      console.error('Stripe not configured - missing STRIPE_SECRET_KEY');
      return NextResponse.json({
        error: '<PERSON>e is not configured. Please add STRIPE_SECRET_KEY to environment variables.'
      }, { status: 500 });
    }
    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get vendor information
    const { data: vendor, error: vendorError } = await supabaseServer
      .from('vendors')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (vendorError || !vendor) {
      return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
    }

    // Check if vendor already has a Stripe account
    if (vendor.stripe_account_id) {
      return NextResponse.json({ 
        error: 'Stripe account already exists',
        accountId: vendor.stripe_account_id 
      }, { status: 400 });
    }

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // You might want to make this dynamic based on vendor location
      email: user.email,
      business_profile: {
        name: vendor.business_name,
        product_description: `${vendor.business_type} services for weddings`,
      },
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });

    // Update vendor record with Stripe account ID
    const { error: updateError } = await supabaseServer
      .from('vendors')
      .update({ 
        stripe_account_id: account.id,
        stripe_onboarding_complete: false 
      })
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating vendor:', updateError);
      return NextResponse.json({ error: 'Failed to update vendor record' }, { status: 500 });
    }

    return NextResponse.json({ 
      accountId: account.id,
      success: true 
    });

  } catch (error) {
    console.error('Error creating Stripe account:', error);
    return NextResponse.json({ 
      error: 'Failed to create Stripe account' 
    }, { status: 500 });
  }
}
