import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json({ 
        error: 'Session ID is required' 
      }, { status: 400 });
    }

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    console.log('sessionnnnn', session)

    if (session.payment_status !== 'paid') {
      return NextResponse.json({ 
        error: 'Payment not completed',
        status: session.payment_status 
      }, { status: 400 });
    }

    // Determine booking type from metadata
    const bookingType = session.metadata?.type;
    const bookingId = session.metadata?.booking_id;
    const productId = session.metadata?.product_id;

    if (!bookingType || !['tent_package_booking', 'product_booking'].includes(bookingType)) {
      return NextResponse.json({ error: 'Invalid booking type' }, { status: 400 });
    }

    if (bookingType === 'tent_package_booking' && !bookingId) {
      return NextResponse.json({ error: 'Tent booking information not found' }, { status: 404 });
    }

    if (bookingType === 'product_booking' && !productId) {
      return NextResponse.json({ error: 'Product booking information not found' }, { status: 404 });
    }

    if (bookingType === 'tent_package_booking') {
      // Get tent booking details from database
      const { data: booking, error: bookingError } = await supabaseServer
        .from('tented_venue_bookings')
        .select(`
          *,
          tent_packages (
            name,
            size,
            capacity,
            price
          ),
          venues (
            name,
            address,
            city,
            state
          )
        `)
        .eq('id', bookingId)
        .single();

      if (bookingError || !booking) {
        return NextResponse.json({ error: 'Tent booking not found' }, { status: 404 });
      }

      // Get couple details
      const { data: couple } = await supabaseServer
        .from('profiles')
        .select('full_name')
        .eq('id', booking.couple_id)
        .single();

      return NextResponse.json({
        success: true,
        bookingType: 'tent_package',
        booking: {
          id: booking.id,
          status: booking.status,
          tentPackage: booking.tent_packages,
          venue: booking.venues,
          couple: couple?.full_name || 'Unknown',
          startDate: booking.start_date,
          endDate: booking.end_date,
        },
        payment: {
          amount: session.amount_total ? session.amount_total / 100 : 0,
          currency: session.currency,
          status: session.payment_status,
        },
      });

    } else if (bookingType === 'product_booking') {
      // Get product details
      const { data: product, error: productError } = await supabaseServer
        .from('products')
        .select('*')
        .eq('id', productId)
        .single();

      if (productError || !product) {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 });
      }

      // Get seller details
      const { data: seller } = await supabaseServer
        .from('profiles')
        .select('full_name')
        .eq('id', product.owner_id)
        .single();

      // Get buyer details
      const buyerId = session.metadata?.buyer_id;
      const { data: buyer } = await supabaseServer
        .from('profiles')
        .select('full_name')
        .eq('id', buyerId)
        .single();

      return NextResponse.json({
        success: true,
        bookingType: 'product',
        booking: {
          id: productId,
          status: product.status,
          product: {
            id: product.id,
            title: product.title,
            description: product.description,
            type: product.type,
            location: product.location,
            reuse_price: product.reuse_price,
            original_price: product.original_price,
          },
          seller: seller?.full_name || 'Unknown',
          buyer: buyer?.full_name || 'Unknown',
        },
        payment: {
          amount: session.amount_total ? session.amount_total / 100 : 0,
          currency: session.currency,
          status: session.payment_status,
        },
      });
    }

    return NextResponse.json({ error: 'Invalid booking type' }, { status: 400 });

  } catch (error) {
    console.error('Error verifying booking session:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to verify booking';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
