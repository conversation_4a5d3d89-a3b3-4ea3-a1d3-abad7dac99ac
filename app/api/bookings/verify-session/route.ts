import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json({ 
        error: 'Session ID is required' 
      }, { status: 400 });
    }

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    console.log('sessionnnnn', session)

    if (session.payment_status !== 'paid') {
      return NextResponse.json({ 
        error: 'Payment not completed',
        status: session.payment_status 
      }, { status: 400 });
    }

    // Get booking details from metadata
    const bookingId = session.metadata?.booking_id;
    
    if (!bookingId) {
      return NextResponse.json({ error: 'Booking information not found' }, { status: 404 });
    }

    // Get booking details from database
    const { data: booking, error: bookingError } = await supabaseServer
      .from('tented_venue_bookings')
      .select(`
        *,
        tent_packages (
          name,
          size,
          capacity,
          price
        ),
        venues (
          name,
          address,
          city,
          state
        )
      `)
      .eq('id', bookingId)
      .single();

    if (bookingError || !booking) {
      return NextResponse.json({ error: 'Booking not found' }, { status: 404 });
    }

    // Get couple details
    const { data: couple, error: coupleError } = await supabaseServer
      .from('profiles')
      .select('full_name')
      .eq('id', booking.couple_id)
      .single();

    return NextResponse.json({
      success: true,
      booking: {
        id: booking.id,
        status: booking.status,
        tentPackage: booking.tent_packages,
        venue: booking.venues,
        couple: couple?.full_name || 'Unknown',
        startDate: booking.start_date,
        endDate: booking.end_date,
      },
      payment: {
        amount: session.amount_total ? session.amount_total / 100 : 0,
        currency: session.currency,
        status: session.payment_status,
      },
    });

  } catch (error) {
    console.error('Error verifying booking session:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to verify booking';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
