import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { productId, sellerId, amount } = await request.json();

    if (!productId || !sellerId || !amount) {
      return NextResponse.json({ 
        error: 'Missing required fields: productId, sellerId, amount' 
      }, { status: 400 });
    }

    // Verify product exists and is available
    const { data: product, error: productError } = await supabaseServer
      .from('products')
      .select('*')
      .eq('id', productId)
      .eq('status', 'available')
      .single();

    if (productError || !product) {
      return NextResponse.json({ error: 'Product not found or not available' }, { status: 404 });
    }

    // Verify the amount matches the product price
    if (product.reuse_price !== amount) {
      return NextResponse.json({ error: 'Amount does not match product price' }, { status: 400 });
    }

    // Get seller details
    const { data: seller, error: sellerError } = await supabaseServer
      .from('profiles')
      .select('full_name')
      .eq('id', sellerId)
      .single();

    if (sellerError || !seller) {
      return NextResponse.json({ error: 'Seller not found' }, { status: 404 });
    }

    // Calculate platform fee (5% of the amount)
    const platformFee = Math.round(amount * 0.05 * 100) / 100;
    const totalAmount = amount + platformFee;

    // Create Stripe Checkout Session for product purchase
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: product.title,
              description: `${product.type} - ${product.description}`,
              images: [], // You can add product images here
            },
            unit_amount: Math.round(amount * 100), // Product price in cents
          },
          quantity: 1,
        },
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Platform Fee',
              description: 'Green Aisle service fee (5%)',
            },
            unit_amount: Math.round(platformFee * 100), // Platform fee in cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      payment_intent_data: {
        capture_method: 'manual', // Escrow behavior - authorize but don't capture
        metadata: {
          type: 'product_booking',
          product_id: productId,
          seller_id: sellerId,
          buyer_id: user.id,
          product_amount: amount.toString(),
          platform_fee: platformFee.toString(),
        },
        description: `Product: ${product.title} from ${seller.full_name}`,
      },
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/booking-success?session_id={CHECKOUT_SESSION_ID}&type=product`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/couple-details/${sellerId}?booking_cancelled=true`,
      metadata: {
        type: 'product_booking',
        product_id: productId,
        seller_id: sellerId,
        buyer_id: user.id,
      },
    });

    // Mark product as pending while payment is being processed
    const { error: updateProductError } = await supabaseServer
      .from('products')
      .update({ 
        status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', productId);

    if (updateProductError) {
      console.error('Error updating product status:', updateProductError);
      // Don't fail the request, but log the error
    }

    // Create transaction record
    const { error: transactionError } = await supabaseServer
      .from('transactions')
      .insert({
        buyer_id: user.id,
        seller_id: sellerId,
        resource_type: 'other', // Products are categorized as 'other'
        resource_id: productId,
        amount: amount,
        platform_fee: platformFee,
        status: 'pending',
        payment_method: 'stripe',
        delivery_method: 'pickup', // Default to pickup, can be updated later
        delivery_notes: `Product purchase: ${product.title}`,
      });

    if (transactionError) {
      console.error('Error creating transaction:', transactionError);
      // Don't fail the request, but log the error
    }

    return NextResponse.json({
      sessionUrl: session.url,
      sessionId: session.id,
      amount: totalAmount,
      platformFee: platformFee,
      productDetails: {
        title: product.title,
        type: product.type,
        description: product.description,
      },
    });

  } catch (error) {
    console.error('Error creating product booking:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to create booking';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
