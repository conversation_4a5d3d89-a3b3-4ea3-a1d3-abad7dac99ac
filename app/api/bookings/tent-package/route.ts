import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    console.log('user', user)
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { tentPackageId, coupleId, venueId, amount } = await request.json();

    if (!tentPackageId || !coupleId || !venueId || !amount) {
      return NextResponse.json({ 
        error: 'Missing required fields: tentPackageId, coupleId, venueId, amount' 
      }, { status: 400 });
    }

    // Verify tent package exists and get details
    const { data: tentPackage, error: packageError } = await supabaseServer
      .from('tent_packages')
      .select('*')
      .eq('id', tentPackageId)
      .single();

    if (packageError || !tentPackage) {
      return NextResponse.json({ error: 'Tent package not found' }, { status: 404 });
    }

    // Verify the amount matches the package price
    if (tentPackage.price !== amount) {
      return NextResponse.json({ error: 'Amount does not match package price' }, { status: 400 });
    }

    // Get couple details for the booking
    const { data: couple, error: coupleError } = await supabaseServer
      .from('profiles')
      .select('full_name')
      .eq('id', coupleId)
      .single();

    if (coupleError || !couple) {
      return NextResponse.json({ error: 'Couple not found' }, { status: 404 });
    }

    // Calculate platform fee (5% of the amount)
    const platformFee = Math.round(amount * 0.05 * 100) / 100;
    const totalAmount = amount + platformFee;

    // Create Stripe Checkout Session for escrow payment
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `Tent Package: ${tentPackage.name}`,
              description: `${tentPackage.size} tent for ${tentPackage.capacity} guests`,
              images: [], // You can add tent package images here
            },
            unit_amount: Math.round(amount * 100), // Package price in cents
          },
          quantity: 1,
        },
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Platform Fee',
              description: 'Green Aisle service fee (5%)',
            },
            unit_amount: Math.round(platformFee * 100), // Platform fee in cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      payment_intent_data: {
        capture_method: 'manual', // Escrow behavior - authorize but don't capture
        metadata: {
          type: 'tent_package_booking',
          tent_package_id: tentPackageId,
          couple_id: coupleId,
          venue_id: venueId,
          buyer_id: user.id,
          booking_id: '', // Will be updated after booking creation
          package_amount: amount.toString(),
          platform_fee: platformFee.toString(),
        },
        description: `Tent Package: ${tentPackage.name} for ${couple.full_name}`,
      },
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/booking-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/couple-details/${coupleId}?booking_cancelled=true`,
      metadata: {
        type: 'tent_package_booking',
        tent_package_id: tentPackageId,
        couple_id: coupleId,
        venue_id: venueId,
        buyer_id: user.id,
      },
    });

    // Create the booking record in database
    console.log('coupleId', coupleId)
    const { data: booking, error: bookingError } = await supabaseServer
      .from('tented_venue_bookings')
      .insert({
        venue_id: venueId,
        couple_id: coupleId,
        tent_package_id: tentPackageId,
        start_date: new Date().toISOString(), // You might want to get actual dates
        end_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Next day
        status: 'pending',
        is_sharing_enabled: true,
      })
      .select()
      .single();

    if (bookingError) {
      console.error('Error creating booking:', bookingError);
      return NextResponse.json({ error: 'Failed to create booking' }, { status: 500 });
    }

    // Create transaction record
    const { error: transactionError } = await supabaseServer
      .from('transactions')
      .insert({
        buyer_id: user.id,
        seller_id: coupleId, // The couple is the "seller" of the tent package access
        resource_type: 'tent',
        resource_id: tentPackageId,
        amount: amount,
        platform_fee: platformFee,
        status: 'pending',
        payment_method: 'stripe',
        delivery_method: 'pickup', // Tent packages are typically pickup
        delivery_notes: `Tent package booking for ${tentPackage.name}`,
      });

    if (transactionError) {
      console.error('Error creating transaction:', transactionError);
      // Don't fail the request, but log the error
    }

    // Update session metadata with booking ID
    await stripe.checkout.sessions.update(session.id, {
      metadata: {
        ...session.metadata,
        booking_id: booking.id,
      },
    });

    return NextResponse.json({
      sessionUrl: session.url,
      sessionId: session.id,
      bookingId: booking.id,
      amount: totalAmount,
      platformFee: platformFee,
      packageDetails: {
        name: tentPackage.name,
        size: tentPackage.size,
        capacity: tentPackage.capacity,
      },
    });

  } catch (error) {
    console.error('Error creating tent package booking:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to create booking';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
