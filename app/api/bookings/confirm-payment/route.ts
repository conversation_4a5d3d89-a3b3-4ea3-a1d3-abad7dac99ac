import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { paymentIntentId, bookingId } = await request.json();

    if (!paymentIntentId || !bookingId) {
      return NextResponse.json({ 
        error: 'Missing required fields: paymentIntentId, bookingId' 
      }, { status: 400 });
    }

    // Verify the booking exists and belongs to the user
    const { data: booking, error: bookingError } = await supabaseServer
      .from('tented_venue_bookings')
      .select('*')
      .eq('id', bookingId)
      .single();

    if (bookingError || !booking) {
      return NextResponse.json({ error: 'Booking not found' }, { status: 404 });
    }

    // In a real implementation, you would:
    // 1. Retrieve the PaymentIntent from Stripe
    // 2. Confirm the payment
    // 3. Capture the payment (for escrow, you might capture later)
    
    try {
      // Retrieve the PaymentIntent
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      if (paymentIntent.status === 'requires_confirmation') {
        // Confirm the payment
        await stripe.paymentIntents.confirm(paymentIntentId);
      }

      // For escrow, we don't capture immediately - the payment is authorized but not captured
      // This allows us to hold the funds and capture them later when the service is delivered
      
    } catch (stripeError) {
      console.error('Stripe error:', stripeError);
      // For demo purposes, we'll continue even if Stripe operations fail
    }

    // Update booking status to confirmed
    const { error: updateBookingError } = await supabaseServer
      .from('tented_venue_bookings')
      .update({ 
        status: 'confirmed',
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);

    if (updateBookingError) {
      console.error('Error updating booking:', updateBookingError);
      return NextResponse.json({ error: 'Failed to confirm booking' }, { status: 500 });
    }

    // Update transaction status to completed
    const { error: updateTransactionError } = await supabaseServer
      .from('transactions')
      .update({ 
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('resource_id', booking.tent_package_id)
      .eq('buyer_id', user.id)
      .eq('status', 'pending');

    if (updateTransactionError) {
      console.error('Error updating transaction:', updateTransactionError);
      // Don't fail the request, but log the error
    }

    // Create notification for the couple
    try {
      const { error: notificationError } = await supabaseServer
        .from('notifications')
        .insert({
          user_id: booking.couple_id,
          type: 'transaction',
          title: 'New Tent Package Booking',
          message: 'Someone has booked your tent package. Payment is held in escrow.',
          action_url: `/bookings/${bookingId}`,
          related_id: bookingId,
        });

      if (notificationError) {
        console.error('Error creating notification:', notificationError);
      }
    } catch (error) {
      console.error('Error creating notification:', error);
    }

    return NextResponse.json({
      success: true,
      bookingId: booking.id,
      status: 'confirmed',
      message: 'Payment confirmed and booking created successfully',
    });

  } catch (error) {
    console.error('Error confirming payment:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to confirm payment';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
