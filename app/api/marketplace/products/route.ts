import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL parameters for filtering
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const status = url.searchParams.get('status') || 'available';
    const search = url.searchParams.get('search');
    const showAll = url.searchParams.get('show_all') === 'true'; // For admin purposes

    // Build query
    let query = supabaseServer
      .from('products')
      .select(`
        id,
        title,
        description,
        reuse_price,
        original_price,
        type,
        status,
        location,
        date_available,
        tags,
        owner_id,
        owner_type,
        created_at,
        owner_profile:profiles!products_owner_id_fkey (
          full_name
        )
      `)
      .eq('status', status)
      .order('created_at', { ascending: false });

    // Apply filters
    if (type && type !== 'all') {
      query = query.eq('type', type);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    const { data: products, error: productsError } = await query;

    if (productsError) {
      console.error('Error fetching products:', productsError);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }

    // Format products for response
    const formattedProducts = (products || []).map(product => ({
      id: product.id,
      title: product.title,
      description: product.description,
      reuse_price: product.reuse_price,
      original_price: product.original_price,
      type: product.type,
      status: product.status,
      location: product.location,
      date_available: product.date_available,
      tags: product.tags || [],
      owner_id: product.owner_id,
      owner_type: product.owner_type,
      owner_name: (product.owner_profile && product.owner_profile[0]?.full_name) || 'Unknown',
      created_at: product.created_at,
    }));

    return NextResponse.json({
      products: formattedProducts,
      total: formattedProducts.length,
    });

  } catch (error) {
    console.error('Error fetching marketplace products:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch products';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
