import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createServerClient } from '@supabase/ssr';

export async function POST(request: NextRequest) {
  try {
    // Check if <PERSON><PERSON> is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not configured');
      return NextResponse.json({ 
        error: 'Payment processing is not configured. Please contact support.' 
      }, { status: 500 });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-02-24.acacia',
    });

    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for POST requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { bookingId, action, bookingType } = await request.json();

    if (!bookingId || !action) {
      return NextResponse.json({
        error: 'Booking ID and action (release/refund) are required'
      }, { status: 400 });
    }

    if (!['release', 'refund'].includes(action)) {
      return NextResponse.json({
        error: 'Action must be either "release" or "refund"'
      }, { status: 400 });
    }

    if (!bookingType || !['tent_package', 'product'].includes(bookingType)) {
      return NextResponse.json({
        error: 'Booking type must be either "tent_package" or "product"'
      }, { status: 400 });
    }

    let booking = null;
    let transaction = null;
    let resourceId = null;

    if (bookingType === 'tent_package') {
      // Get tent booking details
      const { data: tentBooking, error: bookingError } = await supabaseServer
        .from('tented_venue_bookings')
        .select('*')
        .eq('id', bookingId)
        .single();

      if (bookingError || !tentBooking) {
        return NextResponse.json({ error: 'Tent booking not found' }, { status: 404 });
      }

      booking = tentBooking;
      resourceId = booking.tent_package_id;

      // Get transaction details for tent booking
      const { data: tentTransaction, error: transactionError } = await supabaseServer
        .from('transactions')
        .select('*')
        .eq('resource_id', resourceId)
        .eq('resource_type', 'tent')
        .eq('status', 'completed')
        .single();

      if (transactionError || !tentTransaction) {
        return NextResponse.json({ error: 'Tent transaction not found or not in completed status' }, { status: 404 });
      }

      transaction = tentTransaction;

    } else if (bookingType === 'product') {
      // For products, the bookingId is actually the transaction ID
      const { data: productTransaction, error: transactionError } = await supabaseServer
        .from('transactions')
        .select('*')
        .eq('id', bookingId)
        .eq('resource_type', 'other')
        .eq('status', 'pending')
        .single();

      if (transactionError || !productTransaction) {
        return NextResponse.json({ error: 'Product transaction not found or not in completed status' }, { status: 404 });
      }

      transaction = productTransaction;
      resourceId = transaction.resource_id;

      // Get product details
      const { data: product, error: productError } = await supabaseServer
        .from('products')
        .select('*')
        .eq('id', resourceId)
        .single();

      if (productError || !product) {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 });
      }

      booking = { product }; // Store product info in booking object for consistency
    }

    // Find the payment intent from Stripe
    // In a real implementation, you'd store the payment intent ID in your database
    // For now, we'll search for it using metadata
    const paymentIntents = await stripe.paymentIntents.list({
      limit: 100,
    });

    let paymentIntent;

    if (bookingType === 'tent_package') {
      paymentIntent = paymentIntents.data.find(pi =>
        pi.metadata?.tent_package_id === resourceId &&
        pi.metadata?.buyer_id === transaction.buyer_id &&
        pi.status === 'requires_capture'
      );
    } else if (bookingType === 'product') {
      paymentIntent = paymentIntents.data.find(pi =>
        pi.metadata?.product_id === resourceId &&
        pi.metadata?.buyer_id === transaction.buyer_id &&
        pi.status === 'requires_capture'
      );
    }

    if (!paymentIntent) {
      return NextResponse.json({
        error: 'Payment intent not found or already processed'
      }, { status: 404 });
    }

    let result;
    let newTransactionStatus;

    if (action === 'release') {
      // Capture the payment (release to seller)
      result = await stripe.paymentIntents.capture(paymentIntent.id);
      newTransactionStatus = 'released';
    } else {
      // Cancel/refund the payment
      result = await stripe.paymentIntents.cancel(paymentIntent.id);
      newTransactionStatus = 'refunded';
    }

    console.log('newTransactionStatus', newTransactionStatus)

    // Update transaction status
    const { error: updateError } = await supabaseServer
      .from('transactions')
      .update({
        status: newTransactionStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', transaction.id);

    if (updateError) {
      console.error('Error updating transaction:', updateError);
      return NextResponse.json({ error: 'Failed to update transaction status' }, { status: 500 });
    }

    // Update product status if this is a product transaction
    if (bookingType === 'product') {
      const productStatus = action === 'release' ? 'sold' : 'available';

      const { error: productUpdateError } = await supabaseServer
        .from('products')
        .update({
          status: productStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', resourceId);

      if (productUpdateError) {
        console.error('Error updating product status:', productUpdateError);
        return NextResponse.json({ error: 'Failed to update product status' }, { status: 500 });
      }

      console.log(`Product ${resourceId} status updated to ${productStatus}`);
    }

    // Create notifications
    // const notifications = [];

    // // Notify buyer
    // notifications.push({
    //   user_id: transaction.buyer_id,
    //   type: 'transaction',
    //   title: action === 'release' ? 'Payment Released' : 'Payment Refunded',
    //   message: action === 'release' 
    //     ? 'Your payment has been released to the vendor after successful service delivery.'
    //     : 'Your payment has been refunded due to service cancellation.',
    //   action_url: `/bookings/${bookingId}`,
    //   related_id: bookingId,
    // });

    // // Notify seller (couple)
    // notifications.push({
    //   user_id: transaction.seller_id,
    //   type: 'transaction',
    //   title: action === 'release' ? 'Payment Received' : 'Booking Cancelled',
    //   message: action === 'release'
    //     ? 'Payment has been released to your account after successful service delivery.'
    //     : 'The booking has been cancelled and payment refunded to the customer.',
    //   action_url: `/bookings/${bookingId}`,
    //   related_id: bookingId,
    // });

    // // Insert notifications
    // const { error: notificationError } = await supabaseServer
    //   .from('notifications')
    //   .insert(notifications);

    // if (notificationError) {
    //   console.error('Error creating notifications:', notificationError);
    // }

    return NextResponse.json({
      success: true,
      action,
      paymentIntentId: paymentIntent.id,
      status: result.status,
      amount: result.amount / 100,
      transactionStatus: newTransactionStatus,
      message: action === 'release' 
        ? 'Payment successfully released to vendor'
        : 'Payment successfully refunded to customer',
    });

  } catch (error) {
    console.error('Error processing payment action:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to process payment action';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
