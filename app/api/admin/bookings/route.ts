import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all tent package bookings with related data
const { data: bookings, error: bookingsError } = await supabaseServer
  .from('tented_venue_bookings')
  .select(`
    id,
    status,
    start_date,
    end_date,
    created_at,
    couple_id, 
    tent_packages (
      id,
      name,
      size,
      capacity,
      price
    ),
    venues (
      id,
      name,
      address,
      city,
      state
    )
  `)
  .order('created_at', { ascending: false });


    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      return NextResponse.json({ error: 'Failed to fetch bookings' }, { status: 500 });
    }

    console.log('bookings', bookings)
    // Get transaction data for each booking
    const bookingsWithTransactions = await Promise.all(
      (bookings || []).map(async (booking: any) => {
        const { data: transaction } = await supabaseServer
          .from('transactions')
          .select('*')
          .eq('resource_id', booking.tent_packages?.id)
          .eq('resource_type', 'tent')
          .single();

        return {
          id: booking.id,
          status: booking.status,
          tentPackage: {
            id: booking.tent_packages?.id,
            name: booking.tent_packages?.name || 'Unknown Package',
            size: booking.tent_packages?.size,
            capacity: booking.tent_packages?.capacity,
            price: booking.tent_packages?.price || 0,
          },
          venue: {
            id: booking.venues?.id,
            name: booking.venues?.name || 'Unknown Venue',
            address: booking.venues?.address,
            city: booking.venues?.city,
            state: booking.venues?.state,
          },
          couple: booking.couple?.full_name || 'Unknown Couple',
          coupleId: booking.couple?.id,
          startDate: booking.start_date,
          endDate: booking.end_date,
          createdAt: booking.created_at,
          amount: transaction?.amount || booking.tent_packages?.price || 0,
          platformFee: transaction?.platform_fee || 0,
          paymentStatus: transaction?.status === 'completed' ? 'paid' : 'pending',
          transactionStatus: transaction?.status || 'unknown',
        };
      })
    );

    return NextResponse.json({
      bookings: bookingsWithTransactions,
      total: bookingsWithTransactions.length,
    });

  } catch (error) {
    console.error('Error fetching admin bookings:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch bookings';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
