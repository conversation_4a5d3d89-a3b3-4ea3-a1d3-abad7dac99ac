import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }

    // Create server-side Supabase client
    const supabaseServer = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabaseServer.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all tent package bookings with related data
    const { data: tentBookings, error: tentBookingsError } = await supabaseServer
      .from('tented_venue_bookings')
      .select(`
        id,
        status,
        start_date,
        end_date,
        created_at,
        couple_id,
        tent_packages (
          id,
          name,
          size,
          capacity,
          price
        ),
        venues (
          id,
          name,
          address,
          city,
          state
        )
      `)
      .order('created_at', { ascending: false });

    if (tentBookingsError) {
      console.error('Error fetching tent bookings:', tentBookingsError);
    }

    // Get all product transactions
    const { data: productTransactions, error: productTransactionsError } = await supabaseServer
      .from('transactions')
      .select(`
        id,
        amount,
        platform_fee,
        status,
        created_at,
        delivery_method,
        delivery_notes,
        buyer_id,
        seller_id,
        resource_id
      `)
      .eq('resource_type', 'other')
      .order('created_at', { ascending: false });

    if (productTransactionsError) {
      console.error('Error fetching product transactions:', productTransactionsError);
    }

    // Process tent bookings
    const tentBookingsWithTransactions = await Promise.all(
      (tentBookings || []).map(async (booking: any) => {
        const { data: transaction } = await supabaseServer
          .from('transactions')
          .select('*')
          .eq('resource_id', booking.tent_packages?.id)
          .eq('resource_type', 'tent')
          .single();

        // Get couple details
        const { data: couple } = await supabaseServer
          .from('profiles')
          .select('full_name')
          .eq('id', booking.couple_id)
          .single();

        return {
          id: booking.id,
          type: 'tent_package',
          status: booking.status,
          tentPackage: {
            id: booking.tent_packages?.id,
            name: booking.tent_packages?.name || 'Unknown Package',
            size: booking.tent_packages?.size,
            capacity: booking.tent_packages?.capacity,
            price: booking.tent_packages?.price || 0,
          },
          venue: {
            id: booking.venues?.id,
            name: booking.venues?.name || 'Unknown Venue',
            address: booking.venues?.address,
            city: booking.venues?.city,
            state: booking.venues?.state,
          },
          couple: couple?.full_name || 'Unknown Couple',
          coupleId: booking.couple_id,
          startDate: booking.start_date,
          endDate: booking.end_date,
          createdAt: booking.created_at,
          amount: transaction?.amount || booking.tent_packages?.price || 0,
          platformFee: transaction?.platform_fee || 0,
          paymentStatus: transaction?.status === 'completed' ? 'paid' : 'pending',
          transactionStatus: transaction?.status || 'unknown',
        };
      })
    );

    // Process product transactions with separate queries for related data
    const productBookings = await Promise.all(
      (productTransactions || []).map(async (transaction: any) => {
        // Get buyer details
        const { data: buyer } = await supabaseServer
          .from('profiles')
          .select('full_name')
          .eq('id', transaction.buyer_id)
          .single();

        // Get seller details
        const { data: seller } = await supabaseServer
          .from('profiles')
          .select('full_name')
          .eq('id', transaction.seller_id)
          .single();

        // Get product details
        const { data: product } = await supabaseServer
          .from('products')
          .select('id, title, description, type, status, location, reuse_price')
          .eq('id', transaction.resource_id)
          .single();

        return {
          id: transaction.id,
          type: 'product',
          status: product?.status || 'unknown',
          product: {
            id: product?.id,
            title: product?.title || 'Unknown Product',
            description: product?.description,
            type: product?.type,
            location: product?.location,
            price: product?.reuse_price || 0,
          },
          buyer: buyer?.full_name || 'Unknown Buyer',
          seller: seller?.full_name || 'Unknown Seller',
          buyerId: transaction.buyer_id,
          sellerId: transaction.seller_id,
          createdAt: transaction.created_at,
          amount: transaction.amount || 0,
          platformFee: transaction.platform_fee || 0,
          paymentStatus: transaction.status === 'completed' ? 'paid' : 'pending',
          transactionStatus: transaction.status || 'unknown',
          deliveryMethod: transaction.delivery_method,
          deliveryNotes: transaction.delivery_notes,
        };
      })
    );

    // Combine both types of bookings
    const allBookings = [
      ...tentBookingsWithTransactions,
      ...productBookings,
    ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      bookings: allBookings,
      tentBookings: tentBookingsWithTransactions.length,
      productBookings: productBookings.length,
      total: allBookings.length,
    });

  } catch (error) {
    console.error('Error fetching admin bookings:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch bookings';
    return NextResponse.json({ 
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  }
}
