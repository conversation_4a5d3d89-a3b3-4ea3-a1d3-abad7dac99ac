import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function GET() {
  try {
    // Check if Supabase is configured
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return NextResponse.json({
        error: 'Supabase not configured',
        details: 'Missing NEXT_PUBLIC_SUPABASE_URL or NEXT_PUBLIC_SUPABASE_ANON_KEY'
      }, { status: 500 });
    }

    // Create Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return [];
          },
          setAll(_cookiesToSet) {
            // No-op for this test
          },
        },
      }
    );

    // Test vendors table structure
    const { data: vendors, error: vendorsError } = await supabase
      .from('vendors')
      .select('*')
      .limit(1);

    if (vendorsError) {
      return NextResponse.json({
        error: 'Failed to query vendors table',
        details: vendorsError.message
      }, { status: 500 });
    }

    // Check if Stripe fields exist
    const hasStripeFields = vendors && vendors.length > 0 && 
      'stripe_account_id' in vendors[0] && 
      'stripe_onboarding_complete' in vendors[0];

    // Test profiles table structure
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profilesError) {
      return NextResponse.json({
        error: 'Failed to query profiles table',
        details: profilesError.message
      }, { status: 500 });
    }

    return NextResponse.json({
      database: {
        vendors_table: vendors ? '✅ Accessible' : '❌ Not accessible',
        profiles_table: profiles ? '✅ Accessible' : '❌ Not accessible',
        stripe_fields_exist: hasStripeFields ? '✅ Present' : '❌ Missing',
      },
      vendor_sample: vendors && vendors.length > 0 ? Object.keys(vendors[0]) : [],
      profile_sample: profiles && profiles.length > 0 ? Object.keys(profiles[0]) : [],
      status: 'Database test complete',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in database test:', error);
    return NextResponse.json({
      error: 'Database test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
