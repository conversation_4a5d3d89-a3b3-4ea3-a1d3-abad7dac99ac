import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from '@supabase/ssr';


export async function GET(request: NextRequest) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendor_id')

    if (!vendorId) {
      return NextResponse.json(
        { error: 'Vendor ID is required' },
        { status: 400 }
      )
    }

    // Get vendor locations
    const { data: locations, error } = await supabase
      .from('vendor_locations')
      .select('*')
      .eq('vendor_id', vendorId)
      .order('is_primary', { ascending: false })

    if (error) {
      console.error('Error fetching locations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch locations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      locations: locations || []
    })
  } catch (error) {
    console.error('Locations API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      vendor_id,
      location_name,
      city,
      state,
      country,
      service_radius,
      travel_fee,
      is_primary,
      address,
      zip_code,
      notes
    } = body

    // Verify vendor ownership
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id')
      .eq('id', vendor_id)
      .eq('user_id', user.id)
      .single()

    if (vendorError || !vendor) {
      return NextResponse.json(
        { error: 'Vendor not found or unauthorized' },
        { status: 403 }
      )
    }

    // Validate required fields
    if (!location_name || !city || !state) {
      return NextResponse.json(
        { error: 'Location name, city, and state are required' },
        { status: 400 }
      )
    }

    // Check location limit (max 6 as per user requirements)
    const { data: existingLocations, error: countError } = await supabase
      .from('vendor_locations')
      .select('id')
      .eq('vendor_id', vendor_id)

    if (countError) {
      console.error('Error checking location count:', countError)
      return NextResponse.json(
        { error: 'Failed to check location limits' },
        { status: 500 }
      )
    }

    if (existingLocations && existingLocations.length >= 6) {
      return NextResponse.json(
        { error: 'Maximum of 6 locations allowed per vendor' },
        { status: 400 }
      )
    }

    // If this is set as primary, unset other primary locations
    if (is_primary) {
      await supabase
        .from('vendor_locations')
        .update({ is_primary: false })
        .eq('vendor_id', vendor_id)
        .eq('is_primary', true)
    }

    // Insert location
    const { data: location, error: insertError } = await supabase
      .from('vendor_locations')
      .insert({
        vendor_id,
        location_name,
        address: address || null,
        city,
        state,
        country: country || 'USA',
        zip_code: zip_code || null,
        service_radius: service_radius || 50,
        travel_fee: travel_fee || 0,
        is_primary: is_primary || false,
        notes: notes || null
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting location:', insertError)
      return NextResponse.json(
        { error: 'Failed to create location' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Location created successfully',
      location
    })
  } catch (error) {
    console.error('Location creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
