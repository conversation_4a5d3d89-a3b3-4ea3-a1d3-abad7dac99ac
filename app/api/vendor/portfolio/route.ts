import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from '@supabase/ssr';


export async function GET(request: NextRequest) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendor_id')

    if (!vendorId) {
      return NextResponse.json(
        { error: 'Vendor ID is required' },
        { status: 400 }
      )
    }

    // Get portfolio items
    const { data: items, error } = await supabase
      .from('vendor_portfolio')
      .select('*')
      .eq('vendor_id', vendorId)
      .order('display_order', { ascending: true })

    if (error) {
      console.error('Error fetching portfolio:', error)
      return NextResponse.json(
        { error: 'Failed to fetch portfolio' },
        { status: 500 }
      )
    }

    // Get portfolio summary
    const { data: summary, error: summaryError } = await supabase
      .rpc('get_vendor_portfolio_summary', { vendor_uuid: vendorId })

    return NextResponse.json({
      items: items || [],
      summary: summary?.[0] || {
        total_images: 0,
        total_videos: 0,
        featured_items: 0,
        latest_update: null
      }
    })
  } catch (error) {
    console.error('Portfolio API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      vendor_id,
      media_type,
      media_url,
      title,
      description,
      tags,
      is_featured,
      display_order
    } = body

    // Verify vendor ownership
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id')
      .eq('id', vendor_id)
      .eq('user_id', user.id)
      .single()

    if (vendorError || !vendor) {
      return NextResponse.json(
        { error: 'Vendor not found or unauthorized' },
        { status: 403 }
      )
    }

    // Parse tags if it's a string
    let parsedTags = tags
    if (typeof tags === 'string') {
      parsedTags = tags.split(',').map((tag: string) => tag.trim()).filter(Boolean)
    }

    // Get next display order if not provided
    let nextDisplayOrder = display_order
    if (!nextDisplayOrder) {
      const { data: lastItem } = await supabase
        .from('vendor_portfolio')
        .select('display_order')
        .eq('vendor_id', vendor_id)
        .order('display_order', { ascending: false })
        .limit(1)
        .single()

      nextDisplayOrder = (lastItem?.display_order || 0) + 1
    }

    // Insert portfolio item
    const { data: portfolioItem, error: insertError } = await supabase
      .from('vendor_portfolio')
      .insert({
        vendor_id,
        media_type,
        media_url,
        title,
        description,
        tags: parsedTags,
        is_featured: is_featured || false,
        display_order: nextDisplayOrder
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting portfolio item:', insertError)
      return NextResponse.json(
        { error: 'Failed to create portfolio item' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Portfolio item created successfully',
      item: portfolioItem
    })
  } catch (error) {
    console.error('Portfolio creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
