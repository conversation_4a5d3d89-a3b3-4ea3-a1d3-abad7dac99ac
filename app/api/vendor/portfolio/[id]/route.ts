import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from "@supabase/ssr";

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id } = params

    // Get portfolio item and verify ownership
    const { data: portfolioItem, error: fetchError } = await supabase
      .from('vendor_portfolio')
      .select(`
        *,
        vendors!inner (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single()

    if (fetchError || !portfolioItem) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      )
    }

    if (portfolioItem.vendors.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to modify this portfolio item' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    
    if (body.title !== undefined) updateData.title = body.title
    if (body.description !== undefined) updateData.description = body.description
    if (body.is_featured !== undefined) updateData.is_featured = body.is_featured
    if (body.display_order !== undefined) updateData.display_order = body.display_order
    
    if (body.tags !== undefined) {
      if (typeof body.tags === 'string') {
        updateData.tags = body.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean)
      } else {
        updateData.tags = body.tags
      }
    }

    // Add updated_at timestamp
    updateData.updated_at = new Date().toISOString()

    // Update portfolio item
    const { data: updatedItem, error: updateError } = await supabase
      .from('vendor_portfolio')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating portfolio item:', updateError)
      return NextResponse.json(
        { error: 'Failed to update portfolio item' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Portfolio item updated successfully',
      item: updatedItem
    })
  } catch (error) {
    console.error('Portfolio update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params

    // Get portfolio item and verify ownership
    const { data: portfolioItem, error: fetchError } = await supabase
      .from('vendor_portfolio')
      .select(`
        *,
        vendors!inner (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single()

    if (fetchError || !portfolioItem) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      )
    }

    if (portfolioItem.vendors.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this portfolio item' },
        { status: 403 }
      )
    }

    // Extract file path from URL for storage deletion
    let filePath = null
    if (portfolioItem.media_url) {
      try {
        const url = new URL(portfolioItem.media_url)
        const pathParts = url.pathname.split('/')
        const bucketIndex = pathParts.findIndex(part => part === 'aisle')
        if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
          filePath = pathParts.slice(bucketIndex + 1).join('/')
        }
      } catch (urlError) {
        console.warn('Could not parse media URL for file deletion:', urlError)
      }
    }

    // Delete portfolio item from database
    const { error: deleteError } = await supabase
      .from('vendor_portfolio')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting portfolio item:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete portfolio item' },
        { status: 500 }
      )
    }

    // Delete file from storage if path was extracted
    if (filePath) {
      const { error: storageError } = await supabase.storage
        .from('aisle')
        .remove([filePath])

      if (storageError) {
        console.warn('Failed to delete file from storage:', storageError)
        // Don't fail the request if storage deletion fails
      }
    }

    return NextResponse.json({
      message: 'Portfolio item deleted successfully'
    })
  } catch (error) {
    console.error('Portfolio deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
