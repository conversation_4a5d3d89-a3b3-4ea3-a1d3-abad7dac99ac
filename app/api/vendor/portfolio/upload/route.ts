import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from "@supabase/ssr";

export async function POST(request: NextRequest) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const vendorId = formData.get('vendor_id') as string
    const mediaType = formData.get('media_type') as string
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const tags = formData.get('tags') as string
    const isFeatured = formData.get('is_featured') === 'true'

    if (!file || !vendorId || !mediaType) {
      return NextResponse.json(
        { error: 'File, vendor ID, and media type are required' },
        { status: 400 }
      )
    }

    // Verify vendor ownership
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id')
      .eq('id', vendorId)
      .eq('user_id', user.id)
      .single()

    if (vendorError || !vendor) {
      return NextResponse.json(
        { error: 'Vendor not found or unauthorized' },
        { status: 403 }
      )
    }

    // Validate file type
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/mov']
    
    if (mediaType === 'image' && !allowedImageTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid image file type. Allowed: JPEG, PNG, WebP' },
        { status: 400 }
      )
    }

    if (mediaType === 'video' && !allowedVideoTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid video file type. Allowed: MP4, WebM, MOV' },
        { status: 400 }
      )
    }

    // Check file size (10MB for images, 100MB for videos)
    const maxImageSize = 10 * 1024 * 1024 // 10MB
    const maxVideoSize = 100 * 1024 * 1024 // 100MB
    
    if (mediaType === 'image' && file.size > maxImageSize) {
      return NextResponse.json(
        { error: 'Image file too large. Maximum size: 10MB' },
        { status: 400 }
      )
    }

    if (mediaType === 'video' && file.size > maxVideoSize) {
      return NextResponse.json(
        { error: 'Video file too large. Maximum size: 100MB' },
        { status: 400 }
      )
    }

    // Check portfolio limits
    const { data: existingItems, error: countError } = await supabase
      .from('vendor_portfolio')
      .select('media_type')
      .eq('vendor_id', vendorId)

    if (countError) {
      console.error('Error checking portfolio count:', countError)
      return NextResponse.json(
        { error: 'Failed to check portfolio limits' },
        { status: 500 }
      )
    }

    const imageCount = existingItems?.filter(item => item.media_type === 'image').length || 0
    const videoCount = existingItems?.filter(item => item.media_type === 'video').length || 0

    // Enforce limits: 3 images, 2 videos (as per user requirements)
    if (mediaType === 'image' && imageCount >= 3) {
      return NextResponse.json(
        { error: 'Maximum of 3 images allowed in portfolio' },
        { status: 400 }
      )
    }

    if (mediaType === 'video' && videoCount >= 2) {
      return NextResponse.json(
        { error: 'Maximum of 2 videos allowed in portfolio' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const fileName = `${vendorId}/${mediaType}s/${timestamp}.${fileExtension}`

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('vendor-portfolio')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error('Error uploading file:', uploadError)
      return NextResponse.json(
        { error: 'Failed to upload file' },
        { status: 500 }
      )
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('vendor-portfolio')
      .getPublicUrl(fileName)

    // Parse tags
    const parsedTags = tags ? tags.split(',').map(tag => tag.trim()).filter(Boolean) : []

    // Get next display order
    const { data: lastItem } = await supabase
      .from('vendor_portfolio')
      .select('display_order')
      .eq('vendor_id', vendorId)
      .order('display_order', { ascending: false })
      .limit(1)
      .single()

    const nextDisplayOrder = (lastItem?.display_order || 0) + 1

    // Insert portfolio item
    const { data: portfolioItem, error: insertError } = await supabase
      .from('vendor_portfolio')
      .insert({
        vendor_id: vendorId,
        media_type: mediaType,
        media_url: publicUrl,
        title: title || null,
        description: description || null,
        tags: parsedTags,
        is_featured: isFeatured,
        display_order: nextDisplayOrder
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting portfolio item:', insertError)
      
      // Clean up uploaded file if database insert fails
      await supabase.storage
        .from('vendor-portfolio')
        .remove([fileName])

      return NextResponse.json(
        { error: 'Failed to create portfolio item' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'File uploaded successfully',
      item: portfolioItem,
      url: publicUrl
    })
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
