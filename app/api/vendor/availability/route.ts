import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from "@supabase/ssr";

export async function GET(request: NextRequest) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendor_id')

    if (!vendorId) {
      return NextResponse.json(
        { error: 'Vendor ID is required' },
        { status: 400 }
      )
    }

    // Get availability periods with location details
    const { data: availability, error } = await supabase
      .from('vendor_availability')
      .select(`
        *,
        vendor_locations (
          id,
          location_name,
          city,
          state
        )
      `)
      .eq('vendor_id', vendorId)
      .order('start_date', { ascending: true })

    if (error) {
      console.error('Error fetching availability:', error)
      return NextResponse.json(
        { error: 'Failed to fetch availability' },
        { status: 500 }
      )
    }

    // Transform data to include location_name at top level
    const transformedAvailability = availability?.map(period => ({
      ...period,
      location_name: period.vendor_locations?.location_name || 'Unknown Location'
    })) || []

    return NextResponse.json({
      availability: transformedAvailability
    })
  } catch (error) {
    console.error('Availability API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      vendor_id,
      start_date,
      end_date,
      location_id,
      price_per_day,
      minimum_booking_days,
      notes
    } = body

    // Verify vendor ownership
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id')
      .eq('id', vendor_id)
      .eq('user_id', user.id)
      .single()

    if (vendorError || !vendor) {
      return NextResponse.json(
        { error: 'Vendor not found or unauthorized' },
        { status: 403 }
      )
    }

    // Validate dates
    const startDate = new Date(start_date)
    const endDate = new Date(end_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (startDate < today) {
      return NextResponse.json(
        { error: 'Start date cannot be in the past' },
        { status: 400 }
      )
    }

    if (endDate < startDate) {
      return NextResponse.json(
        { error: 'End date must be after start date' },
        { status: 400 }
      )
    }

    // Verify location belongs to vendor
    if (location_id) {
      const { data: location, error: locationError } = await supabase
        .from('vendor_locations')
        .select('id')
        .eq('id', location_id)
        .eq('vendor_id', vendor_id)
        .single()

      if (locationError || !location) {
        return NextResponse.json(
          { error: 'Invalid location for this vendor' },
          { status: 400 }
        )
      }
    }

    // Check for overlapping availability periods
    const { data: overlapping, error: overlapError } = await supabase
      .from('vendor_availability')
      .select('id, start_date, end_date')
      .eq('vendor_id', vendor_id)
      .or(`and(start_date.lte.${end_date},end_date.gte.${start_date})`)

    if (overlapError) {
      console.error('Error checking overlapping periods:', overlapError)
      return NextResponse.json(
        { error: 'Failed to validate availability period' },
        { status: 500 }
      )
    }

    if (overlapping && overlapping.length > 0) {
      return NextResponse.json(
        { error: 'This date range overlaps with existing availability periods' },
        { status: 400 }
      )
    }

    // Insert availability period
    const { data: availabilityPeriod, error: insertError } = await supabase
      .from('vendor_availability')
      .insert({
        vendor_id,
        start_date,
        end_date,
        location_id: location_id || null,
        price_per_day: price_per_day || null,
        minimum_booking_days: minimum_booking_days || 1,
        notes: notes || null,
        status: 'available'
      })
      .select(`
        *,
        vendor_locations (
          id,
          location_name,
          city,
          state
        )
      `)
      .single()

    if (insertError) {
      console.error('Error inserting availability period:', insertError)
      return NextResponse.json(
        { error: 'Failed to create availability period' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Availability period created successfully',
      availability: {
        ...availabilityPeriod,
        location_name: availabilityPeriod.vendor_locations?.location_name || 'Unknown Location'
      }
    })
  } catch (error) {
    console.error('Availability creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
