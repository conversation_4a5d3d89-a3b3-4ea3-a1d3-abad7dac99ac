import { NextRequest, NextResponse } from "next/server"
// import { createClient } from "@/lib/supabase/server"
import { createServerClient } from "@supabase/ssr";

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id } = params

    // Get availability period and verify ownership
    const { data: availabilityPeriod, error: fetchError } = await supabase
      .from('vendor_availability')
      .select(`
        *,
        vendors!inner (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single()

    if (fetchError || !availabilityPeriod) {
      return NextResponse.json(
        { error: 'Availability period not found' },
        { status: 404 }
      )
    }

    if (availabilityPeriod.vendors.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to modify this availability period' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    
    if (body.start_date !== undefined) {
      const startDate = new Date(body.start_date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (startDate < today) {
        return NextResponse.json(
          { error: 'Start date cannot be in the past' },
          { status: 400 }
        )
      }
      updateData.start_date = body.start_date
    }
    
    if (body.end_date !== undefined) {
      updateData.end_date = body.end_date
    }
    
    // Validate date range if both dates are being updated
    if (updateData.start_date && updateData.end_date) {
      const startDate = new Date(updateData.start_date)
      const endDate = new Date(updateData.end_date)
      
      if (endDate < startDate) {
        return NextResponse.json(
          { error: 'End date must be after start date' },
          { status: 400 }
        )
      }
    }
    
    if (body.location_id !== undefined) {
      // Verify location belongs to vendor if provided
      if (body.location_id) {
        const { data: location, error: locationError } = await supabase
          .from('vendor_locations')
          .select('id')
          .eq('id', body.location_id)
          .eq('vendor_id', availabilityPeriod.vendor_id)
          .single()

        if (locationError || !location) {
          return NextResponse.json(
            { error: 'Invalid location for this vendor' },
            { status: 400 }
          )
        }
      }
      updateData.location_id = body.location_id
    }
    
    if (body.price_per_day !== undefined) updateData.price_per_day = body.price_per_day
    if (body.minimum_booking_days !== undefined) updateData.minimum_booking_days = body.minimum_booking_days
    if (body.notes !== undefined) updateData.notes = body.notes
    if (body.status !== undefined) updateData.status = body.status

    // Add updated_at timestamp
    updateData.updated_at = new Date().toISOString()

    // Update availability period
    const { data: updatedPeriod, error: updateError } = await supabase
      .from('vendor_availability')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        vendor_locations (
          id,
          location_name,
          city,
          state
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating availability period:', updateError)
      return NextResponse.json(
        { error: 'Failed to update availability period' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Availability period updated successfully',
      availability: {
        ...updatedPeriod,
        location_name: updatedPeriod.vendor_locations?.location_name || 'Unknown Location'
      }
    })
  } catch (error) {
    console.error('Availability update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables not configured');
      return NextResponse.json({ 
        error: 'Database configuration error' 
      }, { status: 500 });
    }
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // This is a read-only operation for GET requests
          },
        },
      }
    );
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params

    // Get availability period and verify ownership
    const { data: availabilityPeriod, error: fetchError } = await supabase
      .from('vendor_availability')
      .select(`
        *,
        vendors!inner (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single()

    if (fetchError || !availabilityPeriod) {
      return NextResponse.json(
        { error: 'Availability period not found' },
        { status: 404 }
      )
    }

    if (availabilityPeriod.vendors.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this availability period' },
        { status: 403 }
      )
    }

    // Check if period is booked
    if (availabilityPeriod.status === 'booked') {
      return NextResponse.json(
        { error: 'Cannot delete booked availability periods' },
        { status: 400 }
      )
    }

    // Delete availability period
    const { error: deleteError } = await supabase
      .from('vendor_availability')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting availability period:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete availability period' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Availability period deleted successfully'
    })
  } catch (error) {
    console.error('Availability deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
