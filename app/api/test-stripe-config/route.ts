import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check environment variables
    const hasStripeSecret = !!process.env.STRIPE_SECRET_KEY;
    const hasStripePublishable = !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    const hasAppUrl = !!process.env.NEXT_PUBLIC_APP_URL;
    const hasSupabaseUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL;
    const hasSupabaseKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    // Test Stripe initialization
    let stripeInitialized = false;
    let stripeError = null;
    
    if (hasStripeSecret) {
      try {
        const Stripe = require('stripe');
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
          apiVersion: '2025-02-24.acacia',
        });
        
        // Test a simple Stripe API call
        await stripe.balance.retrieve();
        stripeInitialized = true;
      } catch (error) {
        stripeError = error instanceof Error ? error.message : 'Unknown Stripe error';
      }
    }

    return NextResponse.json({
      environment: {
        STRIPE_SECRET_KEY: hasStripeSecret ? '✅ Set' : '❌ Not set',
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: hasStripePublishable ? '✅ Set' : '❌ Not set',
        NEXT_PUBLIC_APP_URL: hasAppUrl ? '✅ Set' : '❌ Not set',
        NEXT_PUBLIC_SUPABASE_URL: hasSupabaseUrl ? '✅ Set' : '❌ Not set',
        NEXT_PUBLIC_SUPABASE_ANON_KEY: hasSupabaseKey ? '✅ Set' : '❌ Not set',
      },
      stripe: {
        initialized: stripeInitialized,
        error: stripeError,
      },
      status: 'API is working',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in test endpoint:', error);
    return NextResponse.json({
      error: 'Test endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
