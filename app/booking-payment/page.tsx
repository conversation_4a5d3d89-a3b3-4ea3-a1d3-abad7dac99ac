"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Leaf,
  CheckCircle,
  Loader2,
  ArrowLeft,
  CreditCard,
  Shield,
  AlertCircle,
  DollarSign,
} from "lucide-react";
import Navbar from "@/components/nav-bar";

export default function BookingPaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const clientSecret = searchParams.get('client_secret');
  const bookingId = searchParams.get('booking_id');

  useEffect(() => {
    if (!clientSecret || !bookingId) {
      setError('Invalid payment session. Please try booking again.');
    }
  }, [clientSecret, bookingId]);

  const handlePayment = async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real implementation, you would use Stripe Elements here
      // For now, we'll simulate the payment process
      
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Call API to confirm payment
      const response = await fetch('/api/bookings/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId: clientSecret?.split('_secret_')[0],
          bookingId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Payment failed');
      }

      setSuccess(true);
      
      // Redirect to success page after a delay
      setTimeout(() => {
        router.push('/booking-success');
      }, 2000);

    } catch (err) {
      console.error('Payment error:', err);
      setError(err instanceof Error ? err.message : 'Payment failed');
    } finally {
      setLoading(false);
    }
  };

  if (!clientSecret || !bookingId) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 py-12 bg-green-50">
          <div className="container max-w-2xl">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Invalid payment session. Please try booking again.
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button asChild variant="outline">
                <Link href="/match">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Matches
                </Link>
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-12 bg-green-50">
        <div className="container max-w-2xl">
          {/* Back Button */}
          <div className="mb-6">
            <Button asChild variant="outline">
              <Link href="/match">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Matches
              </Link>
            </Button>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success ? (
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">Payment Successful!</CardTitle>
                <CardDescription>
                  Your tent package booking has been confirmed. The payment is held in escrow until the event.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span>Payment secured with escrow protection</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Redirecting to booking confirmation...
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-6 w-6 text-blue-600" />
                  Secure Payment
                </CardTitle>
                <CardDescription>
                  Complete your tent package booking with secure escrow payment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Payment Security Info */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-blue-900">Escrow Protection</h3>
                        <p className="text-sm text-blue-700 mt-1">
                          Your payment is held securely until the event is completed. 
                          This protects both you and the couple.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Payment Details */}
                  <div className="space-y-3">
                    <h3 className="font-medium">Payment Summary</h3>
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Tent Package</span>
                        <span>$XXX.XX</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Platform Fee (5%)</span>
                        <span>$XX.XX</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>Total</span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          XXX.XX
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Form Placeholder */}
                  <div className="space-y-4">
                    <h3 className="font-medium">Payment Method</h3>
                    <div className="bg-gray-50 p-6 rounded-lg text-center">
                      <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-600 mb-4">
                        Stripe payment form would be integrated here
                      </p>
                      <p className="text-xs text-gray-500">
                        In production, this would use Stripe Elements for secure card processing
                      </p>
                    </div>
                  </div>

                  {/* Payment Button */}
                  <Button
                    onClick={handlePayment}
                    disabled={loading}
                    className="w-full bg-green-600 hover:bg-green-700"
                    size="lg"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing Payment...
                      </>
                    ) : (
                      <>
                        <Shield className="h-4 w-4 mr-2" />
                        Pay Securely
                      </>
                    )}
                  </Button>

                  {/* Security Notice */}
                  <div className="text-center">
                    <p className="text-xs text-muted-foreground">
                      🔒 Your payment information is encrypted and secure. 
                      Powered by Stripe.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
}
