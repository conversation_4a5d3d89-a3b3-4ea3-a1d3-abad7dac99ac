"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestStripePage() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testStripeConfig = async () => {
    setLoading(true);
    setResult("");
    
    try {
      const response = await fetch('/api/stripe/account-status');
      const data = await response.json();
      
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container max-w-2xl py-8">
      <Card>
        <CardHeader>
          <CardTitle>Stripe Configuration Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testStripeConfig}
            disabled={loading}
          >
            {loading ? "Testing..." : "Test Stripe API"}
          </Button>
          
          {result && (
            <div className="bg-gray-100 p-4 rounded-md">
              <pre className="text-sm overflow-auto">{result}</pre>
            </div>
          )}
          
          <div className="text-sm text-muted-foreground">
            <p><strong>Environment Variables Check:</strong></p>
            <p>STRIPE_SECRET_KEY: {process.env.STRIPE_SECRET_KEY ? '✅ Set' : '❌ Not set'}</p>
            <p>NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: {process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? '✅ Set' : '❌ Not set'}</p>
            <p>NEXT_PUBLIC_APP_URL: {process.env.NEXT_PUBLIC_APP_URL ? '✅ Set' : '❌ Not set'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
