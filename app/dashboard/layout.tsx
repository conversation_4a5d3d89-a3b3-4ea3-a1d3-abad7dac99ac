"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Leaf } from "lucide-react"
import LogoutButton from "@/components/ui/logout-button"
import { useVendorStatus } from "@/hooks/use-vendor-status"
import { Loader2 } from "lucide-react"
import Navbar from "@/components/nav-bar"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { isVendor, loading } = useVendorStatus()

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 py-6 bg-green-50 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading dashboard...</span>
          </div>
        </main>
      </div>
    )
  }

  const isActive = (path: string) => {
    if (path === "/dashboard") {
      return pathname === "/dashboard"
    }
    return pathname.startsWith(path)
  }

  const getLinkClassName = (path: string) => {
    return `text-sm font-medium transition-colors ${
      isActive(path)
        ? "text-foreground"
        : "text-muted-foreground hover:text-foreground"
    }`
  }

  console.log('isVendor', isVendor)
  return (
    <div className="flex min-h-screen flex-col">
      {isVendor ? (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex h-16 items-center justify-between">
            <Link href="/" className="flex items-center gap-2">
              <Leaf className="h-6 w-6 text-green-600" />
              <span className="text-xl font-semibold">Green Aisle</span>
            </Link>
            <nav className="hidden md:flex items-center gap-6">
              <Link 
                href="/dashboard" 
                className={getLinkClassName("/dashboard")}
              >
                Dashboard11
              </Link>
              <Link
                href="/dashboard/portfolio"
                className={getLinkClassName("/dashboard/portfolio")}
              >
                Portfolio
              </Link>
              <Link 
                href="/dashboard/availability" 
                className={getLinkClassName("/dashboard/availability")}
              >
                Availability
              </Link>
              <Link 
                href="/settings" 
                className={getLinkClassName("/settings")}
              >
                Settings
              </Link>
            </nav>
            <div className="flex items-center gap-4">
              <LogoutButton />
            </div>
          </div>
        </header>
      ) : (
        <Navbar />
      )}
      
      <main className="flex-1 py-6 bg-green-50">
        {children}
      </main>
    </div>
  )
}
