"use client"

import { useVendorStatus } from "@/hooks/use-vendor-status"
import { VendorAvailabilityManager } from "@/components/vendor-availability-manager"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function AvailabilityPage() {
  const { vendor, loading } = useVendorStatus()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading availability management...</p>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="container py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Vendor Not Found</h1>
          <p className="text-muted-foreground mb-6">
            Please complete your vendor setup to access availability management.
          </p>
          <Button asChild>
            <Link href="/dashboard">Return to Dashboard</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Availability Management</h1>
        <p className="text-muted-foreground">
          Manage your service locations, availability periods, and booking settings
        </p>
      </div>
      <VendorAvailabilityManager vendorId={vendor.id} />
    </div>
  )
}
