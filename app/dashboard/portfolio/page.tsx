"use client"

import { useVendorStatus } from "@/hooks/use-vendor-status"
import { VendorPortfolioManager } from "@/components/vendor-portfolio-manager"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Leaf } from "lucide-react"
import LogoutButton from "@/components/ui/logout-button"

export default function PortfolioPage() {
  const { vendor, loading } = useVendorStatus()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading portfolio management...</p>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="container py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Vendor Not Found</h1>
          <p className="text-muted-foreground mb-6">
            Please complete your vendor setup to access portfolio management.
          </p>
          <Button asChild>
            <Link href="/dashboard">Return to Dashboard</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">

      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center gap-2">
            <Leaf className="h-6 w-6 text-green-600" />
            <span className="text-xl font-semibold">Green Aisle</span>
          </Link>
          <nav className="hidden md:flex items-center gap-6">
            <Link href="/dashboard" className="text-sm font-medium text-muted-foreground hover:text-foreground">
              Dashboard
            </Link>
            <Link
              href="/dashboard/portfolio"
              className="text-sm font-medium text-foreground"
            >
              Portfolio
            </Link>
            <Link href="/dashboard/availability" className="text-sm font-medium text-muted-foreground hover:text-foreground">
              Availability
            </Link>
            <Link href="/settings" className="text-sm font-medium text-muted-foreground hover:text-foreground">
              Settings
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="container py-6">
        <div className="flex items-center gap-4 mb-6">
          {/* <Button asChild variant="outline" size="sm">
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </Button> */}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Portfolio Management</h1>
            <p className="text-muted-foreground">
              Manage your portfolio images, videos, and showcase your best work
            </p>
          </div>
        </div>
        <VendorPortfolioManager vendorId={vendor.id} />
      </div>

    </div>
  )
}
