"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DollarSign,
  CheckCircle,
  XCircle,
  Loader2,
  AlertCircle,
  CreditCard,
  RefreshCw,
} from "lucide-react";
import Navbar from "@/components/nav-bar";

interface Booking {
  id: string;
  type: 'tent_package' | 'product';
  status: string;
  tentPackage?: {
    name: string;
    price: number;
  };
  product?: {
    title: string;
    price: number;
    type: string;
  };
  venue?: {
    name: string;
  };
  couple?: string;
  buyer?: string;
  seller?: string;
  amount: number;
  paymentStatus: string;
}

export default function PaymentTestingPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<string>("");
  const [selectedAction, setSelectedAction] = useState<string>("");

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/bookings');
      
      if (!response.ok) {
        throw new Error('Failed to fetch bookings');
      }

      const data = await response.json();
      setBookings(data.bookings || []);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch bookings');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentAction = async () => {
    if (!selectedBooking || !selectedAction) {
      setError('Please select a booking and action');
      return;
    }

    try {
      setActionLoading(selectedBooking);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/admin/release-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId: selectedBooking,
          action: selectedAction,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process payment action');
      }

      const data = await response.json();
      setSuccess(data.message);
      
      // Refresh bookings
      await fetchBookings();
      
      // Reset selections
      setSelectedBooking("");
      setSelectedAction("");

    } catch (err) {
      console.error('Error processing payment action:', err);
      setError(err instanceof Error ? err.message : 'Failed to process payment action');
    } finally {
      setActionLoading(null);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-12 bg-green-50">
        <div className="container max-w-6xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold tracking-tight mb-2">
              Payment Testing Dashboard
            </h1>
            <p className="text-muted-foreground">
              Test escrow payment release and refund functionality for tent package bookings.
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}

          {/* Payment Action Controls */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-blue-600" />
                Payment Actions
              </CardTitle>
              <CardDescription>
                Release payments to vendors or refund to customers for testing purposes.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="booking-select">Select Booking</Label>
                  <Select value={selectedBooking} onValueChange={setSelectedBooking}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose booking..." />
                    </SelectTrigger>
                    <SelectContent>
                      {bookings.map((booking) => (
                        <SelectItem key={booking.id} value={booking.id}>
                          {booking.type === 'tent_package'
                            ? `${booking.tentPackage?.name} - $${booking.amount}`
                            : `${booking.product?.title} - $${booking.amount}`
                          }
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="action-select">Action</Label>
                  <Select value={selectedAction} onValueChange={setSelectedAction}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose action..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="release">Release Payment</SelectItem>
                      <SelectItem value="refund">Refund Payment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    onClick={handlePaymentAction}
                    disabled={!selectedBooking || !selectedAction || !!actionLoading}
                    className="w-full"
                  >
                    {actionLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <DollarSign className="h-4 w-4 mr-2" />
                        Execute Action
                      </>
                    )}
                  </Button>
                </div>

                <div className="flex items-end">
                  <Button
                    onClick={fetchBookings}
                    disabled={loading}
                    variant="outline"
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bookings List */}
          <Card>
            <CardHeader>
              <CardTitle>Active Bookings</CardTitle>
              <CardDescription>
                List of tent package bookings with payment status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading bookings...</p>
                </div>
              ) : bookings.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No bookings found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {bookings.map((booking) => (
                    <div
                      key={booking.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">
                            {booking.type === 'tent_package'
                              ? booking.tentPackage?.name || 'Unknown Package'
                              : booking.product?.title || 'Unknown Product'
                            }
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {booking.type === 'tent_package' ? 'Tent' : 'Product'}
                          </Badge>
                          <Badge variant={booking.status === 'confirmed' || booking.status === 'booked' ? 'default' : 'secondary'}>
                            {booking.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {booking.type === 'tent_package'
                            ? `${booking.venue?.name || 'Unknown Venue'} • Couple: ${booking.couple || 'Unknown'}`
                            : `${booking.product?.type || 'Product'} • Seller: ${booking.seller || 'Unknown'} • Buyer: ${booking.buyer || 'Unknown'}`
                          }
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            <span className="font-medium">${booking.amount}</span>
                          </div>
                          <div className="flex items-center gap-1 text-sm">
                            {booking.paymentStatus === 'paid' ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <XCircle className="h-3 w-3 text-red-600" />
                            )}
                            <span className="text-muted-foreground">{booking.paymentStatus}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
