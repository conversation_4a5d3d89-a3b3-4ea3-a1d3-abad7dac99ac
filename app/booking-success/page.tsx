"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Leaf,
  CheckCircle,
  Home,
  Calendar,
  Shield,
  ArrowRight,
  Mail,
  Phone,
} from "lucide-react";
import Navbar from "@/components/nav-bar";

export default function BookingSuccessPage() {
  const router = useRouter();

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-12 bg-green-50">
        <div className="container max-w-2xl">
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Booking Confirmed!</CardTitle>
              <CardDescription>
                Your tent package booking has been successfully confirmed with escrow protection.
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-6">
                {/* Success Details */}
                <div className="space-y-3 text-sm text-muted-foreground">
                  <div className="flex items-center justify-center gap-2">
                    <Home className="h-4 w-4 text-green-600" />
                    <span>Tent package reserved</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <span>Payment secured with escrow protection</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Calendar className="h-4 w-4 text-green-600" />
                    <span>Booking details sent to couple</span>
                  </div>
                </div>

                {/* What Happens Next */}
                <div className="bg-blue-50 p-4 rounded-lg text-left">
                  <h3 className="font-medium text-blue-900 mb-2">What happens next?</h3>
                  <ul className="space-y-2 text-sm text-blue-700">
                    <li className="flex items-start gap-2">
                      <span className="font-medium">1.</span>
                      <span>The couple will be notified of your booking</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-medium">2.</span>
                      <span>Your payment is held securely in escrow</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-medium">3.</span>
                      <span>Coordinate with the couple for setup details</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-medium">4.</span>
                      <span>Payment is released after successful event completion</span>
                    </li>
                  </ul>
                </div>

                {/* Contact Information */}
                <div className="bg-green-50 p-4 rounded-lg text-left">
                  <h3 className="font-medium text-green-900 mb-2">Need help?</h3>
                  <div className="space-y-2 text-sm text-green-700">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <span>1-800-GREEN-AISLE</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    onClick={() => router.push('/dashboard')}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  
                  <Button 
                    asChild
                    variant="outline" 
                    className="w-full"
                  >
                    <Link href="/match">
                      Find More Matches
                    </Link>
                  </Button>
                </div>

                {/* Booking Reference */}
                <div className="text-center pt-4 border-t">
                  <p className="text-xs text-muted-foreground">
                    Booking confirmation will be sent to your email
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
