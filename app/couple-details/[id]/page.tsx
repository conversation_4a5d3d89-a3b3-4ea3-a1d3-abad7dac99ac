"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Leaf,
  Heart,
  MapPin,
  Calendar,
  Users,
  Mail,
  Phone,
  ArrowLeft,
  Home,
  DollarSign,
  CheckCircle,
  Loader2,
  AlertCircle,
  CreditCard,
} from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import Navbar from "@/components/nav-bar";
import { listFiles } from "@/services/bucket.service";
import { getCoupleIdsFromTentedBookings } from "@/services/db.service";

interface CoupleDetails {
  id: string;
  name: string;
  email: string;
  phone?: string;
  weddingDate: string;
  venue: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    isTented: boolean;
  };
  photos: string[];
  description?: string;
  distance: number;
  file: string[];
}

interface TentPackage {
  id: string;
  name: string;
  size: string;
  capacity: number;
  price: number;
  features: string[];
  venueId: string;
}

interface Product {
  id: string;
  title: string;
  description: string;
  reuse_price: number;
  original_price: number;
  type: string;
  status: string;
  location: string;
  date_available: string;
  tags: string[];
}

export default function CoupleDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [coupleDetails, setCoupleDetails] = useState<CoupleDetails | null>(null);
  const [tentPackages, setTentPackages] = useState<TentPackage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<TentPackage | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isTentBooked, setIsTentBooked] = useState<boolean | null>(null);


  const coupleId = params.id as string;

  useEffect(() => {
    if (coupleId) {
      fetchCoupleDetails();
      checkTentBooking();
    }
  }, [coupleId]);

    const checkTentBooking = async () => {
      const tentedCoupleIds = await getCoupleIdsFromTentedBookings();
      setIsTentBooked(tentedCoupleIds.includes(coupleId));
    };

  const fetchCoupleDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/couples/${coupleId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch couple details');
      }

      let data = await response.json();
      const files = await listFiles(data?.couple?.id, "avenue");
      data.couple.file = files;
      
      console.log('data.products', data.products)
      setCoupleDetails(data.couple);
      setProducts(data.products || []);

      // If venue is tented, fetch tent packages
      if (data.couple.venue.isTented) {
        setTentPackages(data.tentPackages || []);
      }
    } catch (err) {
      console.error('Error fetching couple details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load couple details');
    } finally {
      setLoading(false);
    }
  };

  const handleBookTentPackage = async (tentPackage: TentPackage) => {
    try {
      setBookingLoading(true);
      setError(null);
      setSelectedPackage(tentPackage);

      if (!user) {
        setError('Please log in to book a tent package');
        return;
      }

      // Create booking with Stripe escrow payment
      const response = await fetch('/api/bookings/tent-package', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tentPackageId: tentPackage.id,
          coupleId: coupleDetails?.id,
          venueId: tentPackage.venueId,
          amount: tentPackage.price,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create booking');
      }

      const { sessionUrl, sessionId, bookingId } = await response.json();

      // Redirect to Stripe Checkout
      window.location.href = sessionUrl;

    } catch (err) {
      console.error('Error booking tent package:', err);
      setError(err instanceof Error ? err.message : 'Failed to book tent package');
    } finally {
      setBookingLoading(false);
      setSelectedPackage(null);
    }
  };

  const handleBookProduct = async (product: Product) => {
    try {
      setBookingLoading(true);
      setError(null);
      setSelectedProduct(product);

      if (!user) {
        setError('Please log in to book a product');
        return;
      }

      // Create booking with Stripe checkout
      const response = await fetch('/api/bookings/product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product.id,
          sellerId: coupleDetails?.id,
          amount: product.reuse_price,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create booking');
      }

      const { sessionUrl } = await response.json();

      // Redirect to Stripe Checkout
      window.location.href = sessionUrl;

    } catch (err) {
      console.error('Error booking product:', err);
      setError(err instanceof Error ? err.message : 'Failed to book product');
    } finally {
      setBookingLoading(false);
      setSelectedProduct(null);
    }
  };

  const handleContactCouple = () => {
    if (coupleDetails) {
      const subject = encodeURIComponent('Green Aisle - Wedding Collaboration Inquiry');
      const body = encodeURIComponent(
        `Hi ${coupleDetails.name},\n\nI found your wedding details through Green Aisle and would love to discuss potential collaboration opportunities for our upcoming weddings.\n\nBest regards`
      );
      window.location.href = `mailto:${coupleDetails.email}?subject=${subject}&body=${body}`;
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading couple details...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !coupleDetails) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 py-12 bg-green-50">
          <div className="container max-w-2xl">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || 'Couple details not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button asChild variant="outline">
                <Link href="/match">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Matches
                </Link>
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }


return (
  <div className="flex min-h-screen flex-col bg-gradient-to-br from-green-50 via-white to-green-100">
    <Navbar />

    <main className="flex-1 py-12">
      <div className="container">
        {/* Back Button */}
        <div className="mb-6">
          <Button asChild variant="outline" className="hover:bg-green-100">
            <Link href="/match">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Matches
            </Link>
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Section (Image + Details + Description) */}
          <div className="lg:col-span-2">
            <Card className="mb-8 shadow-xl rounded-2xl border-0 bg-white/90">
              <CardHeader>
                <div className="flex flex-col items-center gap-4 w-full">
                  {/* Name and Venue Type */}
                  <div className="flex justify-between gap-2 items-center mt-4 w-full">
                    <CardTitle className="text-3xl flex items-center gap-2">
                      <Heart className="h-7 w-7 text-red-500 animate-pulse" />
                      {coupleDetails.name}
                    </CardTitle>
                    {coupleDetails.venue.isTented && !isTentBooked && (
                      <Badge
                        variant={coupleDetails.venue.isTented ? "default" : "outline"}
                        className={
                          coupleDetails.venue.isTented
                            ? "bg-green-600 text-white border-green-600 mt-2"
                            : "text-green-600 border-green-600 mt-2"
                        }
                      >
                        Tented Venue
                      </Badge>
                    )}
                    
                  </div>

                  {/* Main Image */}
                  <div className="w-full flex justify-center">
                    <div className="w-full aspect-video rounded-2xl overflow-hidden border-4 border-green-200 shadow-lg bg-gray-100">
                      <Image
                        src={coupleDetails.file[0] || "/avatar-placeholder.png"}
                        alt={coupleDetails.name}
                        width={800}
                        height={450}
                        className="object-cover w-full h-full"
                        priority
                      />
                    </div>
                  </div>
 
                  {/* Details */}
                  <div className="flex flex-col md:flex-row md:justify-center gap-4 mt-4 text-gray-700">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span className="font-medium">
                        {new Date(coupleDetails.weddingDate).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>
                        {coupleDetails.venue.name}, {coupleDetails.venue.city}, {coupleDetails.venue.state}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>{coupleDetails.distance.toFixed(1)} miles away</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <Separator className="my-2" />
              <CardContent>
                {/* Gallery */}
                {coupleDetails.file.length > 1 && (
                  <div className="mb-8">
                    <h3 className="font-semibold mb-3 text-green-700">More Wedding Photos</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {coupleDetails.file.slice(1).map((photo, index) => (
                        <div
                          key={index}
                          className="relative aspect-video overflow-hidden rounded-lg shadow hover:scale-105 transition-transform"
                        >
                          <Image
                            src={photo}
                            alt={`${coupleDetails.name} wedding photo ${index + 2}`}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Description */}
                {coupleDetails.description && (
                  <div className="mb-8">
                    <h3 className="font-semibold mb-3 text-green-700">About Their Wedding</h3>
                    <p className="text-muted-foreground">{coupleDetails.description}</p>
                  </div>
                )}

                {/* Contact Actions */}
                <div className="flex flex-col sm:flex-row gap-4 mt-6">
                  <Button
                    onClick={handleContactCouple}
                    variant="outline"
                    className="flex-1 border-green-600 text-green-700 hover:bg-green-50"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Couple
                  </Button>
                  {coupleDetails.phone && (
                    <Button asChild variant="outline" className="flex-1 border-green-600 text-green-700 hover:bg-green-50">
                      <a href={`tel:${coupleDetails.phone}`}>
                        <Phone className="h-4 w-4 mr-2" />
                        Call
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tent Packages Sidebar */}
          {coupleDetails.venue.isTented && tentPackages.length > 0 && !isTentBooked && (
            <div className="lg:col-span-1">
              <Card className="shadow-lg rounded-2xl border-0 bg-green-50/80 ">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-700">
                    <Home className="h-5 w-5 text-blue-600" />
                    Available Tent Packages
                  </CardTitle>
                  <CardDescription>
                    Book a tent package for this venue with secure escrow payment
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {tentPackages.map((tentPackage) => (
                      <Card key={tentPackage.id} className="border-2 border-green-200 rounded-xl shadow hover:shadow-lg transition-shadow">
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            <span>{tentPackage.name}</span>
                            <Badge variant="secondary" className="bg-green-600 text-white">
                              {tentPackage.size}
                            </Badge>
                          </CardTitle>
                          <CardDescription>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              <span>Capacity: {tentPackage.capacity} guests</span>
                            </div>
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {/* Features */}
                            <div>
                              <h4 className="font-medium mb-2 text-green-700">Features:</h4>
                              <ul className="space-y-1">
                                {tentPackage.features.map((feature, index) => (
                                  <li key={index} className="flex items-center gap-2 text-sm">
                                    <CheckCircle className="h-3 w-3 text-green-600" />
                                    <span>{feature}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                            {/* Price and Book Button */}
                            <Separator />
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5 text-green-600" />
                                <span className="text-2xl font-bold">${tentPackage.price}</span>
                              </div>
                              <Button
                                onClick={() => handleBookTentPackage(tentPackage)}
                                disabled={bookingLoading}
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                {bookingLoading && selectedPackage?.id === tentPackage.id ? (
                                  <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Booking...
                                  </>
                                ) : (
                                  <>
                                    <CreditCard className="h-4 w-4 mr-2" />
                                    Book Now
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Product Listings at the End */}
        {products.length > 0 && (
          <Card className="mb-8 mt-10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Available Products
              </CardTitle>
              <CardDescription>
                Wedding items available for reuse from this couple
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                  <Card key={product.id} className="border-2">
                    <CardHeader>
                      <CardTitle className="text-lg">{product.title}</CardTitle>
                      <CardDescription>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline">{product.type}</Badge>
                          <Badge variant={product.status === 'available' ? 'default' : 'secondary'}>
                            {product.status}
                          </Badge>
                        </div>
                        <p className="text-sm">{product.description}</p>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Price Information */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Original Price:</span>
                            <span className="line-through">${product.original_price}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="font-medium">Reuse Price:</span>
                            <span className="text-2xl font-bold text-green-600">${product.reuse_price}</span>
                          </div>
                        </div>
                        {/* Tags */}
                        {product.tags && product.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {product.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                        {/* Location and Date */}
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-3 w-3" />
                            <span>{product.location}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-3 w-3" />
                            <span>Available: {new Date(product.date_available).toLocaleDateString()}</span>
                          </div>
                        </div>
                        {/* Book Button */}
                        {product.status === 'available' ? (
                          <Button
                            onClick={() => handleBookProduct(product)}
                            disabled={bookingLoading}
                            className="w-full bg-green-600 hover:bg-green-700"
                          >
                            {bookingLoading && selectedProduct?.id === product.id ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Booking...
                              </>
                            ) : (
                              <>
                                <CreditCard className="h-4 w-4 mr-2" />
                                Book for ${product.reuse_price}
                              </>
                            )}
                          </Button>
                        ) : product.status === 'booked' ? (
                          <Button
                            disabled
                            className="w-full bg-gray-400 cursor-not-allowed"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Already Booked
                          </Button>
                        ) : product.status === 'pending' ? (
                          <Button
                            disabled
                            className="w-full bg-yellow-500 cursor-not-allowed"
                          >
                            <Loader2 className="h-4 w-4 mr-2" />
                            Payment Processing
                          </Button>
                        ) : (
                          <Button
                            disabled
                            className="w-full bg-gray-400 cursor-not-allowed"
                          >
                            Not Available
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  </div>
);
}
