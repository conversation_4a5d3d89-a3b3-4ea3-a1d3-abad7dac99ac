"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Leaf,
  Heart,
  MapPin,
  Calendar,
  Users,
  Mail,
  Phone,
  ArrowLeft,
  Home,
  DollarSign,
  CheckCircle,
  Loader2,
  AlertCircle,
  CreditCard,
} from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import Navbar from "@/components/nav-bar";
import { listFiles } from "@/services/bucket.service";

interface CoupleDetails {
  id: string;
  name: string;
  email: string;
  phone?: string;
  weddingDate: string;
  venue: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    isTented: boolean;
  };
  photos: string[];
  description?: string;
  distance: number;
  file: string[];
}

interface TentPackage {
  id: string;
  name: string;
  size: string;
  capacity: number;
  price: number;
  features: string[];
  venueId: string;
}

export default function CoupleDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [coupleDetails, setCoupleDetails] = useState<CoupleDetails | null>(null);
  const [tentPackages, setTentPackages] = useState<TentPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<TentPackage | null>(null);

  const coupleId = params.id as string;

  useEffect(() => {
    if (coupleId) {
      fetchCoupleDetails();
    }
  }, [coupleId]);

  const fetchCoupleDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/couples/${coupleId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch couple details');
      }

      let data = await response.json();
      const files = await listFiles(data?.couple?.id, "avenue");
      data.couple.file = files;
      
      setCoupleDetails(data.couple);
      
      // If venue is tented, fetch tent packages
      if (data.couple.venue.isTented) {
        setTentPackages(data.tentPackages || []);
      }
    } catch (err) {
      console.error('Error fetching couple details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load couple details');
    } finally {
      setLoading(false);
    }
  };

  const handleBookTentPackage = async (tentPackage: TentPackage) => {
    try {
      setBookingLoading(true);
      setError(null);
      setSelectedPackage(tentPackage);

      if (!user) {
        setError('Please log in to book a tent package');
        return;
      }

      // Create booking with Stripe escrow payment
      const response = await fetch('/api/bookings/tent-package', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tentPackageId: tentPackage.id,
          coupleId: coupleDetails?.id,
          venueId: tentPackage.venueId,
          amount: tentPackage.price,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create booking');
      }

      const { sessionUrl, sessionId, bookingId } = await response.json();

      // Redirect to Stripe Checkout
      window.location.href = sessionUrl;

    } catch (err) {
      console.error('Error booking tent package:', err);
      setError(err instanceof Error ? err.message : 'Failed to book tent package');
    } finally {
      setBookingLoading(false);
      setSelectedPackage(null);
    }
  };

  const handleContactCouple = () => {
    if (coupleDetails) {
      const subject = encodeURIComponent('Green Aisle - Wedding Collaboration Inquiry');
      const body = encodeURIComponent(
        `Hi ${coupleDetails.name},\n\nI found your wedding details through Green Aisle and would love to discuss potential collaboration opportunities for our upcoming weddings.\n\nBest regards`
      );
      window.location.href = `mailto:${coupleDetails.email}?subject=${subject}&body=${body}`;
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading couple details...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !coupleDetails) {
    return (
      <div className="flex min-h-screen flex-col">
        <Navbar />
        <main className="flex-1 py-12 bg-green-50">
          <div className="container max-w-2xl">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || 'Couple details not found'}
              </AlertDescription>
            </Alert>
            <div className="mt-6">
              <Button asChild variant="outline">
                <Link href="/match">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Matches
                </Link>
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-12 bg-green-50">
        <div className="container max-w-4xl">
          {/* Back Button */}
          <div className="mb-6">
            <Button asChild variant="outline">
              <Link href="/match">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Matches
              </Link>
            </Button>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Couple Details Card */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <Heart className="h-6 w-6 text-red-500" />
                    {coupleDetails.name}
                  </CardTitle>
                  <CardDescription className="mt-2">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Wedding Date: {new Date(coupleDetails.weddingDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{coupleDetails.venue.name}, {coupleDetails.venue.city}, {coupleDetails.venue.state}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{coupleDetails.distance.toFixed(1)} miles away</span>
                      </div>
                    </div>
                  </CardDescription>
                </div>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  {coupleDetails.venue.isTented ? 'Tented Venue' : 'Traditional Venue'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent>
              {/* Photos */}
              {coupleDetails.photos.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-semibold mb-3">Wedding Photos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {coupleDetails.file.map((photo, index) => (
                      <div key={index} className="relative aspect-video overflow-hidden rounded-lg">
                        <Image
                          src={photo}
                          alt={`${coupleDetails.name} wedding photo ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Description */}
              {coupleDetails.description && (
                <div className="mb-6">
                  <h3 className="font-semibold mb-3">About Their Wedding</h3>
                  <p className="text-muted-foreground">{coupleDetails.description}</p>
                </div>
              )}

              {/* Contact Actions */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button onClick={handleContactCouple} variant="outline" className="flex-1">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Couple
                </Button>
                {coupleDetails.phone && (
                  <Button asChild variant="outline" className="flex-1">
                    <a href={`tel:${coupleDetails.phone}`}>
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Tent Packages (if venue is tented) */}
          {coupleDetails.venue.isTented && tentPackages.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Home className="h-5 w-5 text-blue-600" />
                  Available Tent Packages
                </CardTitle>
                <CardDescription>
                  Book a tent package for this venue with secure escrow payment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {tentPackages.map((tentPackage) => (
                    <Card key={tentPackage.id} className="border-2">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>{tentPackage.name}</span>
                          <Badge variant="secondary">{tentPackage.size}</Badge>
                        </CardTitle>
                        <CardDescription>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>Capacity: {tentPackage.capacity} guests</span>
                          </div>
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {/* Features */}
                          <div>
                            <h4 className="font-medium mb-2">Features:</h4>
                            <ul className="space-y-1">
                              {tentPackage.features.map((feature, index) => (
                                <li key={index} className="flex items-center gap-2 text-sm">
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                  <span>{feature}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Price and Book Button */}
                          <Separator />
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-5 w-5 text-green-600" />
                              <span className="text-2xl font-bold">${tentPackage.price}</span>
                            </div>
                            <Button
                              onClick={() => handleBookTentPackage(tentPackage)}
                              disabled={bookingLoading}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {bookingLoading && selectedPackage?.id === tentPackage.id ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Booking...
                                </>
                              ) : (
                                <>
                                  <CreditCard className="h-4 w-4 mr-2" />
                                  Book Now
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
}
