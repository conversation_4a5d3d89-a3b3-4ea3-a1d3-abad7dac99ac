import { notFound } from "next/navigation"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  MapPin,
  Star,
  Calendar,
  DollarSign,
  Phone,
  Mail,
  Globe,
  Camera,
  Video,
  Clock,
  Users,
  Award,
} from "lucide-react"
import { supabase } from "@/lib/supabase";


interface VendorDetailPageProps {
  params: {
    id: string
  }
}

async function getVendorDetails(id: string) {
  
  // Get vendor basic info
  const { data: vendor, error: vendorError } = await supabase
    .from('vendors')
    .select(`
      *
    `)
    .eq('id', id)
    .single()

  if (vendorError || !vendor) {
    return null
  }

  // Get portfolio items
  const { data: portfolio } = await supabase
    .from('vendor_portfolio')
    .select('*')
    .eq('vendor_id', id)
    .order('display_order', { ascending: true })

  // Get locations
  const { data: locations } = await supabase
    .from('vendor_locations')
    .select('*')
    .eq('vendor_id', id)
    .order('is_primary', { ascending: false })

  // Get availability (next 90 days)
  const today = new Date().toISOString().split('T')[0]
  const futureDate = new Date()
  futureDate.setDate(futureDate.getDate() + 90)
  const future = futureDate.toISOString().split('T')[0]

  const { data: availability } = await supabase
    .from('vendor_availability')
    .select(`
      *,
      vendor_locations (
        location_name,
        city,
        state
      )
    `)
    .eq('vendor_id', id)
    .eq('status', 'available')
    .gte('start_date', today)
    .lte('start_date', future)
    .order('start_date', { ascending: true })

  return {
    vendor,
    portfolio: portfolio || [],
    locations: locations || [],
    availability: availability || [],
  }
}

export default async function VendorDetailPage({ params }: VendorDetailPageProps) {
    const { id } = await params
    console.log('id', id)
  const data = await getVendorDetails(id)
  console.log('dataaaaa', data)

  if (!data) {
    notFound()
  }

  const { vendor, portfolio, locations, availability } = data
  const featuredItems = portfolio.filter(item => item.is_featured)
  const images = portfolio.filter(item => item.media_type === 'image')
  const videos = portfolio.filter(item => item.media_type === 'video')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Vendor Info */}
            <div className="lg:col-span-2">
              <div className="flex items-start gap-6">
                <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-green-600">
                    {vendor.business_name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {vendor.business_name}
                  </h1>
                  <div className="flex items-center gap-4 mb-4">
                    <Badge variant="outline" className="text-green-600">
                      {vendor.business_type}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">4.9</span>
                      <span className="text-sm text-gray-500">(127 reviews)</span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-6">{vendor.description}</p>
                  
                  {/* Contact Info */}
                  <div className="flex flex-wrap gap-4">
                    {vendor.phone && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="h-4 w-4" />
                        {vendor.phone}
                      </div>
                    )}
                    {vendor.users?.email && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-4 w-4" />
                        {vendor.users.email}
                      </div>
                    )}
                    {vendor.website && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Globe className="h-4 w-4" />
                        <a href={vendor.website} target="_blank" rel="noopener noreferrer" className="hover:text-green-600">
                          Website
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="space-y-4">
              <Card>
                <CardContent className="p-6">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-green-600">{images.length}</div>
                      <div className="text-sm text-gray-500">Photos</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{videos.length}</div>
                      <div className="text-sm text-gray-500">Videos</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{locations.length}</div>
                      <div className="text-sm text-gray-500">Locations</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{availability.length}</div>
                      <div className="text-sm text-gray-500">Available</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Button className="w-full" size="lg">
                <Calendar className="h-4 w-4 mr-2" />
                Book Now
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Featured Portfolio */}
            {featuredItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    Featured Work
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {featuredItems.slice(0, 6).map((item) => (
                      <div key={item.id} className="aspect-square relative overflow-hidden rounded-lg">
                        {item.media_type === 'image' ? (
                          <Image
                            src={item.media_url}
                            alt={item.title || 'Portfolio item'}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <Video className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        {item.title && (
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
                            <p className="text-sm font-medium truncate">{item.title}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Full Portfolio */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5 text-green-600" />
                  Portfolio
                </CardTitle>
                <CardDescription>
                  Browse all work from {vendor.business_name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {portfolio.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {portfolio.map((item) => (
                      <div key={item.id} className="aspect-square relative overflow-hidden rounded-lg">
                        {item.media_url}
                        {item.media_type === 'image' ? (
                          <Image
                            src={item.media_url}
                            alt={item.title || 'Portfolio item'}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <Video className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        {item.is_featured && (
                          <div className="absolute top-2 left-2">
                            <Badge className="bg-yellow-500 text-white text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Featured
                            </Badge>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No portfolio items available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Service Areas */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-green-600" />
                  Service Areas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {locations.map((location) => (
                    <div key={location.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{location.location_name}</h4>
                        {location.is_primary && (
                          <Badge variant="outline">Primary</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {location.city}, {location.state}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{location.service_radius} mile radius</span>
                        {location.travel_fee > 0 && (
                          <span>${location.travel_fee} travel fee</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Availability */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                  Upcoming Availability
                </CardTitle>
              </CardHeader>
              <CardContent>
                {availability.length > 0 ? (
                  <div className="space-y-3">
                    {availability.slice(0, 5).map((period) => (
                      <div key={period.id} className="border rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Clock className="h-4 w-4 text-green-600" />
                          <span className="font-medium text-sm">
                            {new Date(period.start_date).toLocaleDateString()} - {new Date(period.end_date).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          <div className="flex items-center gap-1 mb-1">
                            <MapPin className="h-3 w-3" />
                            {period.vendor_locations?.location_name}
                          </div>
                          {period.price_per_day && (
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              ${period.price_per_day}/day
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    {availability.length > 5 && (
                      <p className="text-sm text-gray-500 text-center">
                        +{availability.length - 5} more dates available
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No upcoming availability</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact Card */}
            <Card>
              <CardHeader>
                <CardTitle>Get in Touch</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
                <Button variant="outline" className="w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Call Now
                </Button>
                <Button variant="outline" className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  Request Quote
                </Button>
              </CardContent>
            </Card>

            {/* Trust Indicators */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-green-600" />
                  Trust & Safety
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Verified Business</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Background Checked</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Insured & Licensed</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>5+ Years Experience</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
