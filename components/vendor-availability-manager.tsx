"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Calendar,
  MapPin,
  Clock,
  Plus,
  Edit,
  Trash2,
  DollarSign,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface AvailabilityPeriod {
  id: string
  start_date: string
  end_date: string
  location_id: string
  location_name: string
  status: 'available' | 'booked' | 'blocked'
  notes?: string
  price_per_day?: number
  minimum_booking_days: number
}

interface VendorLocation {
  id: string
  location_name: string
  city: string
  state: string
  is_primary: boolean
  service_radius: number
  travel_fee: number
}

interface VendorAvailabilityManagerProps {
  vendorId: string
}

export function VendorAvailabilityManager({ vendorId }: VendorAvailabilityManagerProps) {
  const [availabilityPeriods, setAvailabilityPeriods] = useState<AvailabilityPeriod[]>([])
  const [locations, setLocations] = useState<VendorLocation[]>([])
  const [loading, setLoading] = useState(true)
  const [availabilityDialogOpen, setAvailabilityDialogOpen] = useState(false)
  const [locationDialogOpen, setLocationDialogOpen] = useState(false)
  const { toast } = useToast()

  // Form state for availability
  const [availabilityForm, setAvailabilityForm] = useState({
    start_date: '',
    end_date: '',
    location_id: '',
    price_per_day: '',
    minimum_booking_days: '1',
    notes: '',
  })

  // Form state for location
  const [locationForm, setLocationForm] = useState({
    location_name: '',
    city: '',
    state: '',
    service_radius: '50',
    travel_fee: '0',
    is_primary: false,
  })

  useEffect(() => {
    fetchAvailabilityData()
    fetchLocations()
  }, [vendorId])

  const fetchAvailabilityData = async () => {
    try {
      const response = await fetch(`/api/vendor/availability?vendor_id=${vendorId}`)
      if (response.ok) {
        const data = await response.json()
        setAvailabilityPeriods(data.availability || [])
      }
    } catch (error) {
      console.error('Error fetching availability:', error)
    }
  }

  const fetchLocations = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/vendor/locations?vendor_id=${vendorId}`)
      if (response.ok) {
        const data = await response.json()
        setLocations(data.locations || [])
      }
    } catch (error) {
      console.error('Error fetching locations:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddAvailability = async () => {
    try {
      const response = await fetch('/api/vendor/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          vendor_id: vendorId,
          ...availabilityForm,
          price_per_day: parseFloat(availabilityForm.price_per_day) || null,
          minimum_booking_days: parseInt(availabilityForm.minimum_booking_days),
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Availability period added",
        })
        setAvailabilityDialogOpen(false)
        setAvailabilityForm({
          start_date: '',
          end_date: '',
          location_id: '',
          price_per_day: '',
          minimum_booking_days: '1',
          notes: '',
        })
        fetchAvailabilityData()
      } else {
        throw new Error('Failed to add availability')
      }
    } catch (error) {
      console.error('Error adding availability:', error)
      toast({
        title: "Error",
        description: "Failed to add availability period",
        variant: "destructive",
      })
    }
  }

  const handleAddLocation = async () => {
    try {
      const response = await fetch('/api/vendor/locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          vendor_id: vendorId,
          ...locationForm,
          service_radius: parseInt(locationForm.service_radius),
          travel_fee: parseFloat(locationForm.travel_fee),
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Location added",
        })
        setLocationDialogOpen(false)
        setLocationForm({
          location_name: '',
          city: '',
          state: '',
          service_radius: '50',
          travel_fee: '0',
          is_primary: false,
        })
        fetchLocations()
      } else {
        throw new Error('Failed to add location')
      }
    } catch (error) {
      console.error('Error adding location:', error)
      toast({
        title: "Error",
        description: "Failed to add location",
        variant: "destructive",
      })
    }
  }

  const handleDeleteAvailability = async (id: string) => {
    try {
      const response = await fetch(`/api/vendor/availability/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Availability period deleted",
        })
        fetchAvailabilityData()
      }
    } catch (error) {
      console.error('Error deleting availability:', error)
      toast({
        title: "Error",
        description: "Failed to delete availability period",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'text-green-600 bg-green-100'
      case 'booked':
        return 'text-orange-600 bg-orange-100'
      case 'blocked':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Availability Management</h2>
          <p className="text-muted-foreground">
            Manage your available dates and service locations
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={locationDialogOpen} onOpenChange={setLocationDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Add Location
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Service Location</DialogTitle>
                <DialogDescription>
                  Add a new location where you provide services
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="location-name">Location Name</Label>
                  <Input
                    id="location-name"
                    value={locationForm.location_name}
                    onChange={(e) =>
                      setLocationForm({ ...locationForm, location_name: e.target.value })
                    }
                    placeholder="e.g., Downtown Studio"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={locationForm.city}
                      onChange={(e) =>
                        setLocationForm({ ...locationForm, city: e.target.value })
                      }
                      placeholder="San Francisco"
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={locationForm.state}
                      onChange={(e) =>
                        setLocationForm({ ...locationForm, state: e.target.value })
                      }
                      placeholder="CA"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="service-radius">Service Radius (miles)</Label>
                    <Input
                      id="service-radius"
                      type="number"
                      value={locationForm.service_radius}
                      onChange={(e) =>
                        setLocationForm({ ...locationForm, service_radius: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="travel-fee">Travel Fee ($)</Label>
                    <Input
                      id="travel-fee"
                      type="number"
                      step="0.01"
                      value={locationForm.travel_fee}
                      onChange={(e) =>
                        setLocationForm({ ...locationForm, travel_fee: e.target.value })
                      }
                    />
                  </div>
                </div>
                <Button onClick={handleAddLocation} className="w-full">
                  Add Location
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={availabilityDialogOpen} onOpenChange={setAvailabilityDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Availability
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Availability Period</DialogTitle>
                <DialogDescription>
                  Set your available dates and pricing
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={availabilityForm.start_date}
                      onChange={(e) =>
                        setAvailabilityForm({ ...availabilityForm, start_date: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={availabilityForm.end_date}
                      onChange={(e) =>
                        setAvailabilityForm({ ...availabilityForm, end_date: e.target.value })
                      }
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Select
                    value={availabilityForm.location_id}
                    onValueChange={(value) =>
                      setAvailabilityForm({ ...availabilityForm, location_id: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      {locations.map((location) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.location_name} - {location.city}, {location.state}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price-per-day">Price per Day ($)</Label>
                    <Input
                      id="price-per-day"
                      type="number"
                      step="0.01"
                      value={availabilityForm.price_per_day}
                      onChange={(e) =>
                        setAvailabilityForm({ ...availabilityForm, price_per_day: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="min-days">Minimum Days</Label>
                    <Input
                      id="min-days"
                      type="number"
                      value={availabilityForm.minimum_booking_days}
                      onChange={(e) =>
                        setAvailabilityForm({ ...availabilityForm, minimum_booking_days: e.target.value })
                      }
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={availabilityForm.notes}
                    onChange={(e) =>
                      setAvailabilityForm({ ...availabilityForm, notes: e.target.value })
                    }
                    placeholder="Any special notes or requirements"
                  />
                </div>
                <Button onClick={handleAddAvailability} className="w-full">
                  Add Availability
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Availability Periods */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Availability Periods
          </CardTitle>
          <CardDescription>
            Your scheduled availability and bookings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {availabilityPeriods.map((period) => (
              <div key={period.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <Clock className="h-4 w-4 text-green-600" />
                    <span className="font-medium">
                      {formatDate(period.start_date)} - {formatDate(period.end_date)}
                    </span>
                    <Badge className={getStatusColor(period.status)}>
                      {period.status}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeleteAvailability(period.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {period.location_name}
                  </span>
                  {period.price_per_day && (
                    <span className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      ${period.price_per_day}/day
                    </span>
                  )}
                  <span>Min {period.minimum_booking_days} days</span>
                </div>
                {period.notes && (
                  <p className="text-sm text-muted-foreground mt-2">{period.notes}</p>
                )}
              </div>
            ))}

            {availabilityPeriods.length === 0 && (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No availability periods set
                </h3>
                <p className="text-gray-500 mb-4">
                  Add your available dates to start receiving bookings
                </p>
                <Button onClick={() => setAvailabilityDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Period
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Service Locations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Service Locations
          </CardTitle>
          <CardDescription>
            Areas where you provide services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {locations.map((location) => (
              <div key={location.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{location.location_name}</h4>
                  {location.is_primary && (
                    <Badge variant="outline">Primary</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {location.city}, {location.state}
                </p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>{location.service_radius} mile radius</span>
                  <span>${location.travel_fee} travel fee</span>
                </div>
              </div>
            ))}

            {locations.length === 0 && (
              <div className="col-span-2 text-center py-8">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No service locations added
                </h3>
                <p className="text-gray-500 mb-4">
                  Add locations where you provide services
                </p>
                <Button onClick={() => setLocationDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Location
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
