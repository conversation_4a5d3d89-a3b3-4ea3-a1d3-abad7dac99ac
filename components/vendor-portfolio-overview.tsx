"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Camera, Video, MapPin, Calendar, Plus, Eye } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface PortfolioItem {
  id: string
  title: string
  description: string
  media_url: string
  media_type: 'image' | 'video'
  is_featured: boolean
  created_at: string
}

interface Location {
  id: string
  location_name: string
  city: string
  state: string
  is_primary: boolean
}

interface AvailabilityPeriod {
  id: string
  start_date: string
  end_date: string
  status: string
  location_name: string
}

interface VendorPortfolioOverviewProps {
  vendorId: string
}

export function VendorPortfolioOverview({ vendorId }: VendorPortfolioOverviewProps) {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [availabilityPeriods, setAvailabilityPeriods] = useState<AvailabilityPeriod[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOverviewData()
  }, [vendorId])

  const fetchOverviewData = async () => {
    try {
      // Fetch portfolio items
      const portfolioResponse = await fetch('/api/vendor/portfolio')
      console.log('portfolioResponse', portfolioResponse)
      if (portfolioResponse.ok) {
        const portfolioData = await portfolioResponse.json()
        console.log('portfolioData', portfolioData)
        setPortfolioItems(portfolioData.items || [])
      }

      // Fetch locations
      const locationsResponse = await fetch('/api/vendor/locations')
      if (locationsResponse.ok) {
        const locationsData = await locationsResponse.json()
        setLocations(locationsData || [])
      }

      // Fetch availability
      const availabilityResponse = await fetch('/api/vendor/availability')
      if (availabilityResponse.ok) {
        const availabilityData = await availabilityResponse.json()
        setAvailabilityPeriods(availabilityData || [])
      }
    } catch (error) {
      console.error('Error fetching overview data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading portfolio overview...</p>
        </div>
      </div>
    )
  }

  const featuredItems = portfolioItems.filter(item => item.is_featured)
  const imageCount = portfolioItems.filter(item => item.media_type === 'image').length
  const videoCount = portfolioItems.filter(item => item.media_type === 'video').length
  const primaryLocation = locations.find(loc => loc.is_primary)
  const upcomingAvailability = availabilityPeriods.filter(period => 
    new Date(period.start_date) > new Date() && period.status === 'available'
  ).slice(0, 3)

  return (
    <div className="space-y-6">
      {/* Portfolio Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Portfolio Items</CardTitle>
            <Camera className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{portfolioItems.length}</div>
            <p className="text-xs text-muted-foreground">
              {imageCount} images, {videoCount} videos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Service Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{locations.length}</div>
            <p className="text-xs text-muted-foreground">
              {primaryLocation ? `Primary: ${primaryLocation.city}` : 'No primary set'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Availability</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availabilityPeriods.length}</div>
            <p className="text-xs text-muted-foreground">
              {upcomingAvailability.length} upcoming periods
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured Work</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{featuredItems.length}</div>
            <p className="text-xs text-muted-foreground">
              Highlighted portfolio items
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Featured Portfolio Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Featured Portfolio</CardTitle>
            <p className="text-sm text-muted-foreground">Your highlighted work</p>
          </div>
          <Button asChild variant="outline">
            <Link href="/dashboard/portfolio">
              <Plus className="h-4 w-4 mr-2" />
              Manage Portfolio
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          {featuredItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {featuredItems.slice(0, 3).map((item) => (
                <div key={item.id} className="relative group">
                  <div className="aspect-square relative overflow-hidden rounded-lg">
                    {item.media_type === 'image' ? (
                      <Image
                        src={item.media_url}
                        alt={item.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <Video className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                    <Badge className="absolute top-2 right-2 bg-green-600">
                      Featured
                    </Badge>
                  </div>
                  <div className="mt-2">
                    <h4 className="font-medium text-sm">{item.title}</h4>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No featured portfolio items
              </h3>
              <p className="text-gray-500 mb-4">
                Add some portfolio items and mark them as featured to showcase your best work
              </p>
              <Button asChild>
                <Link href="/dashboard/portfolio">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Portfolio Items
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Service Locations
            </CardTitle>
          </CardHeader>
          <CardContent>
            {locations.length > 0 ? (
              <div className="space-y-2">
                {locations.slice(0, 3).map((location) => (
                  <div key={location.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{location.location_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {location.city}, {location.state}
                      </p>
                    </div>
                    {location.is_primary && (
                      <Badge variant="outline" className="text-xs">Primary</Badge>
                    )}
                  </div>
                ))}
                {locations.length > 3 && (
                  <p className="text-xs text-muted-foreground">
                    +{locations.length - 3} more locations
                  </p>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No service locations added</p>
            )}
            <Button asChild variant="outline" className="w-full mt-4">
              <Link href="/dashboard/availability">Manage Locations</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Availability
            </CardTitle>
          </CardHeader>
          <CardContent>
            {upcomingAvailability.length > 0 ? (
              <div className="space-y-2">
                {upcomingAvailability.map((period) => (
                  <div key={period.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">
                        {new Date(period.start_date).toLocaleDateString()} - {new Date(period.end_date).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {period.location_name}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">Available</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No upcoming availability</p>
            )}
            <Button asChild variant="outline" className="w-full mt-4">
              <Link href="/dashboard/availability">Manage Availability</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
