"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Upload,
  Camera,
  Video,
  Plus,
  Edit,
  Trash2,
  Eye,
  Star,
  X,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface PortfolioItem {
  id: string
  media_type: 'image' | 'video'
  media_url: string
  title?: string
  description?: string
  display_order: number
  is_featured: boolean
  tags: string[]
}

interface VendorPortfolioManagerProps {
  vendorId: string
}

export function VendorPortfolioManager({ vendorId }: VendorPortfolioManagerProps) {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([])
  const [loading, setLoading] = useState(true)
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<PortfolioItem | null>(null)
  const { toast } = useToast()

  // Form state
  const [formData, setFormData] = useState({
    media_type: 'image' as 'image' | 'video',
    title: '',
    description: '',
    tags: '',
    is_featured: false,
  })

  useEffect(() => {
    fetchPortfolioItems()
  }, [vendorId])

  const fetchPortfolioItems = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/vendor/portfolio?vendor_id=${vendorId}`)
      if (response.ok) {
        const data = await response.json()
        setPortfolioItems(data.items || [])
      }
    } catch (error) {
      console.error('Error fetching portfolio:', error)
      toast({
        title: "Error",
        description: "Failed to load portfolio items",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (file: File) => {
    try {
      const formDataUpload = new FormData()
      formDataUpload.append('file', file)
      formDataUpload.append('vendor_id', vendorId)
      formDataUpload.append('media_type', formData.media_type)
      formDataUpload.append('title', formData.title)
      formDataUpload.append('description', formData.description)
      formDataUpload.append('tags', formData.tags)
      formDataUpload.append('is_featured', formData.is_featured.toString())

      const response = await fetch('/api/vendor/portfolio/upload', {
        method: 'POST',
        body: formDataUpload,
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Media uploaded successfully",
        })
        setUploadDialogOpen(false)
        setFormData({
          media_type: 'image',
          title: '',
          description: '',
          tags: '',
          is_featured: false,
        })
        fetchPortfolioItems()
      } else {
        throw new Error('Upload failed')
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast({
        title: "Error",
        description: "Failed to upload media",
        variant: "destructive",
      })
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    try {
      const response = await fetch(`/api/vendor/portfolio/${itemId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Portfolio item deleted",
        })
        fetchPortfolioItems()
      } else {
        throw new Error('Delete failed')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      })
    }
  }

  const toggleFeatured = async (itemId: string, currentFeatured: boolean) => {
    try {
      const response = await fetch(`/api/vendor/portfolio/${itemId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_featured: !currentFeatured,
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: `Item ${!currentFeatured ? 'featured' : 'unfeatured'}`,
        })
        fetchPortfolioItems()
      }
    } catch (error) {
      console.error('Error updating featured status:', error)
      toast({
        title: "Error",
        description: "Failed to update item",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Portfolio Management</h2>
          <p className="text-muted-foreground">
            Showcase your work with images and videos
          </p>
        </div>
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Media
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Upload Media</DialogTitle>
              <DialogDescription>
                Add images or videos to your portfolio
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="media-type">Media Type</Label>
                <Select
                  value={formData.media_type}
                  onValueChange={(value: 'image' | 'video') =>
                    setFormData({ ...formData, media_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="image">Image</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Enter title"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Enter description"
                />
              </div>
              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  value={formData.tags}
                  onChange={(e) =>
                    setFormData({ ...formData, tags: e.target.value })
                  }
                  placeholder="wedding, flowers, decoration"
                />
              </div>
              <div>
                <Label htmlFor="file">Choose File</Label>
                <Input
                  id="file"
                  type="file"
                  accept={formData.media_type === 'image' ? 'image/*' : 'video/*'}
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      handleFileUpload(file)
                    }
                  }}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Portfolio Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {portfolioItems.map((item) => (
          <Card key={item.id} className="overflow-hidden group">
            <div className="aspect-square relative">
              {item.media_type === 'image' ? (
                <Image
                  src={item.media_url}
                  alt={item.title || 'Portfolio item'}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                  <Video className="h-12 w-12 text-gray-400" />
                </div>
              )}
              
              {/* Featured badge */}
              {item.is_featured && (
                <div className="absolute top-2 left-2">
                  <Badge className="bg-yellow-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>
              )}

              {/* Overlay actions */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 flex gap-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => toggleFeatured(item.id, item.is_featured)}
                  >
                    <Star className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="secondary">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeleteItem(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            {item.title && (
              <CardContent className="p-3">
                <h4 className="font-medium text-sm truncate">{item.title}</h4>
                {item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {item.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{item.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        ))}

        {/* Add new item placeholder */}
        <Card className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors cursor-pointer">
          <div
            className="aspect-square flex items-center justify-center"
            onClick={() => setUploadDialogOpen(true)}
          >
            <div className="text-center">
              <Plus className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Add Media</p>
            </div>
          </div>
        </Card>
      </div>

      {portfolioItems.length === 0 && (
        <div className="text-center py-12">
          <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No portfolio items yet
          </h3>
          <p className="text-gray-500 mb-4">
            Start building your portfolio by uploading your best work
          </p>
          <Button onClick={() => setUploadDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Item
          </Button>
        </div>
      )}
    </div>
  )
}
