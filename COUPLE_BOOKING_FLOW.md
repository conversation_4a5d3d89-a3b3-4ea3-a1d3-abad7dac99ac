# Couple Details & Tent Booking Flow

This document explains the implementation of the couple details page and tent package booking system with Stripe escrow payments.

## Overview

The system allows users to:
1. **View Couple Details** - See detailed information about matched couples
2. **Browse Tent Packages** - View available tent packages for tented venues
3. **Book with Escrow** - Secure booking with Stripe escrow payment protection
4. **Track Transactions** - Complete transaction and booking management

## User Flow

### 1. Couple Matching → Details
- From `/match` page, click "View Couple Details" on any couple card
- Navigate to `/couple-details/[id]` with the couple's user ID

### 2. Couple Details Page
- **Couple Information**: Name, wedding date, venue details, distance
- **Photos**: Wedding venue and setup photos
- **Contact Options**: Email and phone contact buttons
- **Tent Packages**: Available packages if venue is tented

### 3. Tent Package Booking
- **Package Selection**: Choose from available tent packages
- **Secure Payment**: Stripe escrow payment processing
- **Booking Confirmation**: Success page with next steps

## Implementation Details

### Files Created

#### Pages
- `app/couple-details/[id]/page.tsx` - Couple details and tent packages
- `app/booking-payment/page.tsx` - Payment processing page
- `app/booking-success/page.tsx` - Booking confirmation page

#### API Routes
- `app/api/couples/[id]/route.ts` - Fetch couple details and tent packages
- `app/api/bookings/tent-package/route.ts` - Create booking with Stripe escrow
- `app/api/bookings/confirm-payment/route.ts` - Confirm payment and update records

### Database Operations

#### Tables Used
- **`profiles`** - Couple information
- **`weddings`** - Wedding details and venue relationships
- **`venues`** - Venue information and tented status
- **`tent_packages`** - Available tent packages per venue
- **`tented_venue_bookings`** - Booking records
- **`transactions`** - Payment and transaction tracking
- **`notifications`** - User notifications

#### Key Relationships
```sql
-- Couple → Wedding → Venue → Tent Packages
profiles.id → weddings.couple_id
weddings.venue_id → venues.id
venues.id → tent_packages.venue_id

-- Booking → Transaction tracking
tented_venue_bookings.id → transactions.resource_id
```

### Stripe Escrow Implementation

#### Payment Intent Creation
```typescript
const paymentIntent = await stripe.paymentIntents.create({
  amount: Math.round(totalAmount * 100), // Convert to cents
  currency: 'usd',
  payment_method_types: ['card'],
  capture_method: 'manual', // Escrow behavior
  metadata: {
    type: 'tent_package_booking',
    tent_package_id: tentPackageId,
    couple_id: coupleId,
    buyer_id: user.id,
  },
});
```

#### Escrow Process
1. **Authorization**: Payment method is authorized but not captured
2. **Hold Funds**: Money is held by Stripe (not captured)
3. **Event Completion**: After successful event, capture the payment
4. **Dispute Protection**: Funds can be released or refunded based on outcome

### API Endpoints

#### GET /api/couples/[id]
**Purpose**: Fetch couple details and tent packages

**Response**:
```json
{
  "couple": {
    "id": "uuid",
    "name": "Couple Name",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "weddingDate": "2024-06-15T00:00:00Z",
    "venue": {
      "id": "venue-uuid",
      "name": "Venue Name",
      "address": "123 Main St",
      "city": "City",
      "state": "State",
      "isTented": true
    },
    "photos": ["url1", "url2"],
    "description": "Wedding description",
    "distance": 5.2
  },
  "tentPackages": [
    {
      "id": "package-uuid",
      "name": "Premium Tent Package",
      "size": "40x60",
      "capacity": 150,
      "price": 2500,
      "features": ["Climate control", "Lighting", "Flooring"],
      "venueId": "venue-uuid"
    }
  ]
}
```

#### POST /api/bookings/tent-package
**Purpose**: Create tent package booking with escrow payment

**Request**:
```json
{
  "tentPackageId": "package-uuid",
  "coupleId": "couple-uuid", 
  "venueId": "venue-uuid",
  "amount": 2500
}
```

**Response**:
```json
{
  "paymentIntentClientSecret": "pi_xxx_secret_xxx",
  "bookingId": "booking-uuid",
  "amount": 2625, // Including platform fee
  "platformFee": 125,
  "packageDetails": {
    "name": "Premium Tent Package",
    "size": "40x60",
    "capacity": 150
  }
}
```

#### POST /api/bookings/confirm-payment
**Purpose**: Confirm payment and update booking status

**Request**:
```json
{
  "paymentIntentId": "pi_xxx",
  "bookingId": "booking-uuid"
}
```

**Response**:
```json
{
  "success": true,
  "bookingId": "booking-uuid",
  "status": "confirmed",
  "message": "Payment confirmed and booking created successfully"
}
```

### Transaction Flow

#### 1. Booking Creation
```sql
INSERT INTO tented_venue_bookings (
  venue_id, couple_id, tent_package_id, 
  start_date, end_date, status
) VALUES (
  'venue-id', 'couple-id', 'package-id',
  '2024-06-15', '2024-06-16', 'pending'
);
```

#### 2. Transaction Record
```sql
INSERT INTO transactions (
  buyer_id, seller_id, resource_type, resource_id,
  amount, platform_fee, status, payment_method
) VALUES (
  'buyer-id', 'couple-id', 'tent', 'package-id',
  2500, 125, 'pending', 'stripe'
);
```

#### 3. Payment Confirmation
```sql
-- Update booking status
UPDATE tented_venue_bookings 
SET status = 'confirmed' 
WHERE id = 'booking-id';

-- Update transaction status  
UPDATE transactions 
SET status = 'completed' 
WHERE resource_id = 'package-id' AND buyer_id = 'buyer-id';
```

### Security Features

#### Authentication & Authorization
- All API routes require valid user authentication
- Users can only view public couple profiles
- Booking creation requires authenticated user
- Payment confirmation validates booking ownership

#### Payment Security
- Stripe handles all payment processing
- No card details stored in application
- Escrow protection for both parties
- Platform fee calculation server-side

#### Data Validation
- Input validation on all API endpoints
- Amount verification against package price
- Booking existence verification
- User permission checks

### Error Handling

#### Common Error Scenarios
- **Couple Not Found**: Invalid couple ID
- **Venue Not Tented**: No tent packages available
- **Payment Failed**: Stripe payment processing errors
- **Booking Conflict**: Package already booked for dates
- **Authentication**: User not logged in

#### Error Responses
```json
{
  "error": "Error message",
  "details": "Development details (dev mode only)"
}
```

### Future Enhancements

#### Payment Features
- **Partial Payments**: Allow deposits and payment plans
- **Refund Processing**: Automated refund handling
- **Dispute Resolution**: Built-in dispute management
- **Multi-Currency**: Support for international payments

#### Booking Features
- **Calendar Integration**: Real-time availability checking
- **Booking Modifications**: Change dates or packages
- **Cancellation Policy**: Automated cancellation handling
- **Insurance Options**: Optional booking insurance

#### Communication Features
- **In-App Messaging**: Direct couple communication
- **Booking Updates**: Real-time status notifications
- **Review System**: Post-event reviews and ratings
- **Photo Sharing**: Event photo sharing platform
