# Product Booking System Implementation

This document explains the complete product listing and booking system for couples' wedding items with Stripe integration and marketplace functionality.

## Overview

The system now includes:

1. **Product Listings on Couple Details** - View products listed by specific couples
2. **Marketplace Product Booking** - Browse and book all available products
3. **Real Stripe Integration** - Actual payment processing with escrow
4. **Automatic Status Management** - Products marked as booked after purchase
5. **Availability Filtering** - Booked products hidden from listings

## Key Features Implemented

### 1. Couple Details Page Product Listings

#### Location: `/couple-details/[id]`

**Features:**
- **Product Display**: Shows all products listed by the couple
- **Product Information**: Title, description, original price, reuse price, type, tags
- **Availability Status**: Real-time availability checking
- **Direct Booking**: One-click booking with Stripe checkout
- **Location & Date**: Product pickup location and availability date

**Product Card Information:**
```typescript
interface Product {
  id: string;
  title: string;
  description: string;
  reuse_price: number;
  original_price: number;
  type: string;
  status: string; // 'available', 'booked', 'pending'
  location: string;
  date_available: string;
  tags: string[];
}
```

### 2. Enhanced Marketplace with Booking

#### Location: `/marketplace`

**New Features:**
- **Real Booking Functionality**: Enabled booking buttons on product cards
- **Loading States**: Visual feedback during booking process
- **Error Handling**: Comprehensive error messages and alerts
- **Status Filtering**: Only shows available products
- **Real-time Updates**: Products disappear after booking

**Updated FloralCard Component:**
- **Booking Button**: Replaces "View Details" with "Book for $X"
- **Loading State**: Shows spinner during booking process
- **Status Awareness**: Disables booking for unavailable items
- **Price Display**: Clear pricing with original vs reuse price

### 3. Real Stripe Payment Integration

#### Product Booking Flow:
```typescript
// 1. Create Stripe Checkout Session
const session = await stripe.checkout.sessions.create({
  payment_method_types: ['card'],
  line_items: [
    {
      price_data: {
        currency: 'usd',
        product_data: {
          name: product.title,
          description: `${product.type} - ${product.description}`,
        },
        unit_amount: Math.round(amount * 100), // Convert to cents
      },
      quantity: 1,
    },
    // Platform fee line item
  ],
  mode: 'payment',
  payment_intent_data: {
    capture_method: 'manual', // Escrow behavior
    metadata: {
      type: 'product_booking',
      product_id: productId,
      seller_id: sellerId,
      buyer_id: user.id,
    },
  },
  success_url: `${APP_URL}/booking-success?session_id={CHECKOUT_SESSION_ID}&type=product`,
  cancel_url: `${APP_URL}/couple-details/${sellerId}?booking_cancelled=true`,
});

// 2. Redirect to Stripe Checkout
window.location.href = session.url;
```

### 4. Automatic Status Management

#### Product Status Flow:
1. **Available** → Product listed and bookable
2. **Pending** → Payment being processed (temporary)
3. **Booked** → Payment successful, product no longer available
4. **Available** → Payment failed, product becomes available again

#### Database Updates:
```sql
-- On payment success
UPDATE products 
SET status = 'booked', updated_at = NOW() 
WHERE id = product_id;

-- On payment failure
UPDATE products 
SET status = 'available', updated_at = NOW() 
WHERE id = product_id;
```

### 5. Webhook Integration

#### Real-time Status Updates:
```typescript
// Webhook handles Stripe events
case 'checkout.session.completed':
  if (session.metadata?.type === 'product_booking') {
    await handleProductBookingSuccess(session, supabase);
  }

case 'payment_intent.payment_failed':
  if (failedPayment.metadata?.type === 'product_booking') {
    await handleProductBookingFailure(failedPayment, supabase);
  }
```

## File Structure

### New/Updated Files

#### API Routes
- `app/api/bookings/product/route.ts` - Product booking with Stripe
- `app/api/marketplace/products/route.ts` - Marketplace product listing
- `app/api/stripe/webhook/route.ts` - Enhanced webhook for products
- `app/api/couples/[id]/route.ts` - Updated to include products

#### Pages
- `app/couple-details/[id]/page.tsx` - Enhanced with product listings
- `app/marketplace/page.tsx` - Updated with booking functionality
- `app/booking-success/page.tsx` - Updated for product bookings

#### Components
- `components/floral-card.tsx` - Enhanced with booking functionality

## Database Schema

### Products Table
```sql
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  reuse_price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  type TEXT NOT NULL,
  status TEXT DEFAULT 'available', -- 'available', 'booked', 'pending'
  location TEXT,
  date_available TIMESTAMP,
  tags TEXT[],
  owner_id UUID REFERENCES profiles(id),
  owner_type TEXT DEFAULT 'couple',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Transactions Table
```sql
-- Enhanced to handle product bookings
INSERT INTO transactions (
  buyer_id,
  seller_id,
  resource_type, -- 'other' for products
  resource_id,   -- product_id
  amount,
  platform_fee,
  status,        -- 'pending' → 'completed'
  payment_method,
  delivery_method,
  delivery_notes
);
```

## User Experience Flow

### 1. Couple Details Product Booking
1. **Navigate** to couple details page
2. **Browse Products** listed by the couple
3. **Click "Book for $X"** on desired product
4. **Redirect to Stripe** for payment processing
5. **Complete Payment** with real card details
6. **Return to Success Page** with confirmation
7. **Product Status** automatically updated to "booked"

### 2. Marketplace Product Booking
1. **Browse Marketplace** with all available products
2. **Filter by Category** or search for specific items
3. **Click "Book for $X"** on any product
4. **Stripe Checkout** for secure payment
5. **Automatic Status Update** removes product from listings
6. **Seller Notification** about the purchase

### 3. Availability Management
- **Real-time Filtering**: Booked products hidden from all listings
- **Status Synchronization**: Immediate updates across all pages
- **Concurrent Booking Prevention**: Products locked during payment
- **Automatic Cleanup**: Failed payments restore availability

## Testing the System

### 1. Test Product Display
1. **Create Products** for a couple in the database
2. **Navigate** to couple details page
3. **Verify Products** are displayed with correct information
4. **Check Availability** status and pricing

### 2. Test Booking Flow
1. **Click "Book"** on any available product
2. **Verify Redirect** to Stripe Checkout
3. **Use Test Card**: `****************`
4. **Complete Payment** and verify redirect
5. **Check Product Status** updated to "booked"

### 3. Test Marketplace Integration
1. **Navigate** to `/marketplace`
2. **Verify Products** from all couples are listed
3. **Test Booking** functionality
4. **Verify Filtering** hides booked products

### 4. Test Error Scenarios
1. **Payment Decline**: Use `****************`
2. **Verify Product** returns to available status
3. **Test Concurrent Booking** attempts
4. **Verify Error Messages** are displayed

## Stripe Test Cards

### Successful Payments
- **Visa**: `****************`
- **Visa (debit)**: `****************`
- **Mastercard**: `****************`

### Failed Payments
- **Generic decline**: `****************`
- **Insufficient funds**: `****************`
- **Lost card**: `****************`

## Environment Variables

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

## Security Features

### Payment Security
- **Server-side Processing**: All Stripe operations on server
- **Escrow Protection**: Payments held until service delivery
- **Webhook Verification**: Signature validation for authenticity
- **Metadata Validation**: Ownership and amount verification

### Data Protection
- **Authentication Required**: All booking operations require login
- **Input Validation**: Server-side validation of all inputs
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Input sanitization and output encoding

## Future Enhancements

### Advanced Features
1. **Product Images**: Upload and display product photos
2. **Product Categories**: Enhanced categorization and filtering
3. **Product Reviews**: Buyer reviews and ratings
4. **Bulk Booking**: Multiple product purchases in one transaction

### Business Features
1. **Commission Tracking**: Detailed platform fee reporting
2. **Seller Dashboard**: Product management interface
3. **Analytics**: Sales and booking analytics
4. **Inventory Management**: Stock tracking and alerts

### User Experience
1. **Wishlist**: Save products for later
2. **Comparison**: Compare multiple products
3. **Recommendations**: AI-powered product suggestions
4. **Mobile App**: Native mobile experience
