# Stripe Integration Setup

This document explains how to set up the Stripe integration for vendor payments in the Green Aisle application.

## Overview

The Stripe integration allows vendors to:
- Create Stripe Express accounts for payment processing
- Complete onboarding to receive payments
- Automatically redirect to setup if onboarding is incomplete

## Setup Instructions

### 1. Install Dependencies

The Stripe dependency has been added to package.json. Install it by running:

```bash
npm install
```

### 2. Environment Variables

Add the following environment variables to your `.env.local` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# App Configuration (required for Stripe redirects)
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Database Migration

Run the database migration to add Stripe fields to the vendors table:

```sql
-- Add Stripe-related fields to vendors table
ALTER TABLE public.vendors 
ADD COLUMN stripe_account_id TEXT,
ADD COLUMN stripe_onboarding_complete BOOLEAN DEFAULT FALSE;

-- Add index for faster lookups
CREATE INDEX idx_vendors_stripe_account_id ON public.vendors(stripe_account_id);
CREATE INDEX idx_vendors_user_id_stripe ON public.vendors(user_id, stripe_onboarding_complete);
```

### 4. Stripe Account Setup

1. Create a Stripe account at https://stripe.com
2. Get your API keys from the Stripe Dashboard
3. Set up webhooks (optional, for production)

## How It Works

### Vendor Onboarding Flow

1. **Vendor Registration**: When a vendor completes onboarding, they are redirected to `/vendor-stripe-setup`
2. **Account Creation**: The system creates a Stripe Express account for the vendor
3. **Onboarding**: Vendor completes Stripe's onboarding process
4. **Verification**: System verifies completion and updates database
5. **Dashboard Access**: Vendor can access full dashboard features

### API Routes

- `POST /api/stripe/create-account` - Creates a new Stripe Express account
- `POST /api/stripe/create-account-link` - Creates onboarding link for existing account
- `GET /api/stripe/account-status` - Checks account status and onboarding completion

### Middleware Protection

The middleware automatically:
- Redirects incomplete vendors to Stripe setup
- Allows access to setup pages for authenticated vendors
- Prevents access to dashboard until setup is complete

## Pages Added

- `/vendor-stripe-setup` - Main setup page with account creation and status
- `/vendor-stripe-setup/success` - Success page after completing onboarding

## Components Added

- `VendorDashboard` - Vendor-specific dashboard with payment status
- `useVendorStatus` hook - Manages vendor status and Stripe information

## Testing

1. Use Stripe test keys for development
2. Test the complete flow: registration → Stripe setup → dashboard access
3. Verify that incomplete vendors are redirected appropriately

## Production Considerations

1. Use live Stripe keys in production
2. Set up proper webhook endpoints for real-time status updates
3. Configure proper domain for `NEXT_PUBLIC_APP_URL`
4. Consider adding additional error handling and logging

## Troubleshooting

- **Redirect loops**: Check that environment variables are set correctly
- **Account creation fails**: Verify Stripe API keys and permissions
- **Onboarding not completing**: Check Stripe dashboard for account status
- **Database errors**: Ensure migration has been run successfully
