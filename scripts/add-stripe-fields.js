require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function addStripeFields() {
  try {
    console.log('Adding Stripe fields to vendors table...');
    
    // Check if columns already exist
    const { data: existingColumns, error: checkError } = await supabase
      .from('vendors')
      .select('*')
      .limit(1);
    
    if (checkError) {
      console.error('Error checking existing columns:', checkError);
      return;
    }
    
    if (existingColumns && existingColumns.length > 0) {
      const firstRow = existingColumns[0];
      if ('stripe_account_id' in firstRow && 'stripe_onboarding_complete' in firstRow) {
        console.log('✅ Stripe fields already exist in vendors table');
        return;
      }
    }
    
    console.log('⚠️  Stripe fields not found. You need to run the SQL migration manually.');
    console.log('Please run this SQL in your Supabase SQL editor:');
    console.log(`
-- Add Stripe-related fields to vendors table
ALTER TABLE public.vendors 
ADD COLUMN IF NOT EXISTS stripe_account_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_onboarding_complete BOOLEAN DEFAULT FALSE;

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_vendors_stripe_account_id ON public.vendors(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_vendors_user_id_stripe ON public.vendors(user_id, stripe_onboarding_complete);
    `);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

addStripeFields();
