# Subscription Flow Implementation

This document explains the complete subscription flow for vendors, including plan selection, payment processing, and access control.

## Overview

Vendors must select and pay for a subscription plan before accessing the dashboard. The flow includes:

1. **Plan Selection** - Choose from Free, Pro, or Unlimited plans
2. **Payment Processing** - Stripe Checkout for paid plans
3. **Stripe Account Setup** - Payment processing setup after subscription
4. **Dashboard Access** - Full access after completing both steps

## Subscription Plans

### Plan Details

| Plan | Product ID | Price | Features |
|------|------------|-------|----------|
| **Free Plan** | `prod_SU5v6PMffy7iBw` | $0/forever | 3 services, basic features |
| **Pro Plan** | `prod_SU5wgCT5gxG1By` | $29/month | Unlimited services, advanced features |
| **Unlimited Tier** | `prod_SU5xPY7ZRcxDsC` | $99/month | Everything + white-label, API access |

### Plan Features

#### Free Plan
- List up to 3 wedding services
- Basic profile customization
- Access to floral marketplace
- Email support
- Basic analytics

#### Pro Plan (Most Popular)
- Unlimited wedding services
- Advanced profile customization
- Priority marketplace placement
- Couple coordination tools
- Advanced analytics & insights
- Priority email support
- Custom booking forms

#### Unlimited Tier
- Everything in Pro Plan
- Unlimited marketplace listings
- Advanced couple matching
- White-label solutions
- API access
- Dedicated account manager
- Custom integrations
- 24/7 phone support

## Implementation Details

### Files Created

- `app/subscription-plans/page.tsx` - Plan selection interface
- `app/subscription-success/page.tsx` - Payment confirmation page
- `app/api/subscription/select-plan/route.ts` - Free plan selection
- `app/api/subscription/create-checkout/route.ts` - Stripe checkout creation
- `app/api/subscription/verify/route.ts` - Payment verification

### Database Schema

Added to `vendors` table:
```sql
subscription_plan_id TEXT,           -- Stripe product ID
subscription_status TEXT DEFAULT 'inactive',  -- active/inactive/canceled
stripe_subscription_id TEXT,        -- Stripe subscription ID
stripe_customer_id TEXT             -- Stripe customer ID
```

### User Flow

#### 1. Vendor Registration
- Complete onboarding form
- Redirected to `/subscription-plans`

#### 2. Plan Selection
- **Free Plan**: Direct database update, redirect to Stripe setup
- **Paid Plans**: Create Stripe checkout session, redirect to Stripe

#### 3. Payment Processing (Paid Plans)
- Stripe Checkout handles payment
- Webhook or verification updates database
- Redirect to `/subscription-success`

#### 4. Stripe Account Setup
- After subscription, redirect to `/vendor-stripe-setup`
- Create Stripe Express account for receiving payments
- Complete Stripe onboarding

#### 5. Dashboard Access
- Only available after both subscription and Stripe setup complete

### Middleware Protection

The middleware enforces the subscription flow:

```typescript
// Check vendor status and redirect accordingly
if (profile?.user_type === 'vendor') {
  // No subscription -> subscription plans
  if (!vendor.subscription_status || vendor.subscription_status === 'inactive') {
    return NextResponse.redirect('/subscription-plans')
  }
  
  // Has subscription but no Stripe -> Stripe setup
  if (vendor.subscription_status === 'active' && !vendor.stripe_onboarding_complete) {
    return NextResponse.redirect('/vendor-stripe-setup')
  }
}
```

### API Routes

#### POST /api/subscription/select-plan
- Handles free plan selection
- Updates vendor subscription status
- Returns success confirmation

#### POST /api/subscription/create-checkout
- Creates Stripe checkout session for paid plans
- Includes metadata for tracking
- Returns checkout URL

#### POST /api/subscription/verify
- Verifies payment completion
- Updates vendor subscription details
- Returns subscription information

## Stripe Integration

### Checkout Session Configuration

```typescript
const session = await stripe.checkout.sessions.create({
  customer_email: user.email,
  payment_method_types: ['card'],
  line_items: [{ price: price.id, quantity: 1 }],
  mode: 'subscription',
  success_url: `${APP_URL}/subscription-success?session_id={CHECKOUT_SESSION_ID}`,
  cancel_url: `${APP_URL}/subscription-plans?canceled=true`,
  metadata: {
    user_id: user.id,
    vendor_id: vendor.id,
    plan_id: planId,
  },
});
```

### Subscription Verification

```typescript
const session = await stripe.checkout.sessions.retrieve(sessionId);
const subscription = await stripe.subscriptions.retrieve(session.subscription);
const product = await stripe.products.retrieve(subscription.items.data[0].price.product);

// Update database with subscription details
await supabase
  .from('vendors')
  .update({
    subscription_tier: planName,
    subscription_plan_id: product.id,
    subscription_status: 'active',
    stripe_subscription_id: subscription.id,
    stripe_customer_id: session.customer,
  })
  .eq('user_id', user.id);
```

## Testing

### Test Flow
1. Register as vendor
2. Select subscription plan
3. Complete payment (use Stripe test cards)
4. Verify subscription activation
5. Complete Stripe account setup
6. Access dashboard

### Test Cards
- **Success**: `****************`
- **Decline**: `****************`
- **3D Secure**: `****************`

## Environment Variables

Required for subscription functionality:

```env
# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# App
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Future Enhancements

- **Webhooks**: Real-time subscription status updates
- **Plan Changes**: Upgrade/downgrade functionality
- **Usage Tracking**: Monitor plan limits and usage
- **Billing Portal**: Customer self-service billing
- **Proration**: Handle mid-cycle plan changes
