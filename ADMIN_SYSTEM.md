# Green Aisle Admin System

This document describes the comprehensive admin authentication and authorization system implemented for Green Aisle.

## Overview

The admin system provides secure access to administrative functions including payment management, user oversight, and system administration. It uses a multi-layered approach combining email-based identification, database role verification, and route-level protection.

## Architecture

### 1. Admin User Types

The system supports three user types in the database:
- `couple` - Regular couple users
- `vendor` - Vendor users with subscription requirements
- `admin` - Administrative users with elevated privileges

### 2. Admin Email Configuration

Admin emails are configured in `lib/auth/admin.ts`:

```typescript
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Add more admin emails here as needed
];
```

### 3. Authentication Flow

1. **Login**: Admin users login with their email/password
2. **Email Check**: System checks if email is in `ADMIN_EMAILS` array
3. **Profile Verification**: System verifies user has `user_type = 'admin'` in database
4. **Auto-Redirect**: Admin users are automatically redirected to `/admin/payment-testing`
5. **Profile Creation**: If admin email exists but profile is missing, it's created automatically

## Components

### Core Files

#### `lib/auth/admin.ts`
- Admin email configuration
- Server-side admin verification functions
- Middleware helper functions

#### `hooks/use-admin.ts`
- Client-side admin status checking
- Real-time admin verification
- Profile data access

#### `components/admin-navbar.tsx`
- Admin-specific navigation component
- Admin badge and branding
- Secure logout functionality

#### `lib/supabase/middleware.ts`
- Route protection for admin paths
- Automatic admin profile creation
- Admin redirect logic

### Database Schema

#### Profiles Table Update
```sql
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_user_type_check 
CHECK (user_type IN ('couple', 'vendor', 'admin'));
```

#### Admin Policies
```sql
-- Admin users can view all profiles
CREATE POLICY "Admin users can view all profiles"
    ON public.profiles FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );
```

## Security Features

### 1. Multi-Layer Authentication
- Email whitelist verification
- Database role verification
- Session-based authentication

### 2. Route Protection
- Middleware-level admin route protection
- API endpoint admin verification
- Client-side access control

### 3. Automatic Profile Management
- Auto-creation of admin profiles for whitelisted emails
- Profile synchronization on login
- Graceful handling of missing profiles

## Admin Features

### 1. Payment Dashboard (`/admin/payment-testing`)
- View all tent bookings and product purchases
- Release escrow payments to vendors/sellers
- Process refunds to customers
- Real-time transaction status updates
- Unified booking management interface

### 2. Enhanced Permissions
- Access to all user profiles
- View all booking and transaction data
- Override normal user restrictions
- System-wide administrative controls

### 3. Admin Navigation
- Dedicated admin navbar with admin badge
- Quick access to admin functions
- Secure logout with proper session cleanup

## API Endpoints

### Protected Admin Routes

#### `GET /api/admin/bookings`
- Returns all bookings (tent packages and products)
- Requires admin authentication
- Unified booking data format

#### `POST /api/admin/release-payment`
- Releases escrow payments to vendors
- Updates booking/transaction status
- Requires admin authentication
- Supports both tent and product bookings

### Authentication Check
All admin API routes include:
```typescript
const isAdmin = await isCurrentUserAdmin(request);
if (!isAdmin) {
  return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
}
```

## Setup Instructions

### 1. Database Migration
Run the migration to add admin user type:
```bash
# Apply the migration
supabase db push
```

### 2. Create Admin User
1. Create user in Supabase Auth with email `<EMAIL>`
2. Run SQL to create admin profile:
```sql
INSERT INTO public.profiles (id, full_name, user_type, created_at, updated_at)
VALUES ('USER_UUID', 'Green Aisle Admin', 'admin', NOW(), NOW());
```

### 3. Configure Admin Emails
Add admin emails to `lib/auth/admin.ts`:
```typescript
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
];
```

## Usage

### Admin Login
1. Navigate to `/login`
2. Enter admin email and password
3. Automatically redirected to `/admin/payment-testing`

### Managing Payments
1. View all pending transactions
2. Select booking and action (release/refund)
3. Execute payment action
4. Monitor real-time status updates

### Adding New Admins
1. Add email to `ADMIN_EMAILS` array
2. Create user in Supabase Auth
3. User will be auto-promoted on first login

## Error Handling

### Common Issues

#### Access Denied
- Verify email is in `ADMIN_EMAILS` array
- Check user profile has `user_type = 'admin'`
- Ensure user is properly authenticated

#### API 403 Errors
- Confirm admin authentication is working
- Check middleware configuration
- Verify database policies are correct

#### Profile Creation Failures
- Check database permissions
- Verify RLS policies allow profile creation
- Ensure user UUID is correct

## Security Considerations

### 1. Email Verification
- Admin emails are hardcoded in application
- No dynamic admin promotion
- Requires code deployment to add new admins

### 2. Database Security
- RLS policies enforce admin access patterns
- Admin profiles are protected from modification
- Audit trail for admin actions

### 3. Session Management
- Standard Supabase session handling
- Secure logout clears all session data
- Automatic session refresh

## Monitoring

### Admin Actions
- All admin API calls are logged
- Payment actions create audit trails
- Error tracking for admin operations

### Access Patterns
- Monitor admin login frequency
- Track admin route access
- Alert on unusual admin activity

## Future Enhancements

### Planned Features
1. **Admin User Management**: Interface to manage other admin users
2. **Audit Logging**: Comprehensive admin action logging
3. **Role-Based Permissions**: Granular admin permission system
4. **Admin Dashboard**: Enhanced admin overview dashboard
5. **System Monitoring**: Real-time system health monitoring

### Scalability
- Support for multiple admin roles
- Department-based admin access
- Integration with external identity providers
