-- Add subscription-related fields to vendors table
ALTER TABLE public.vendors 
ADD COLUMN IF NOT EXISTS subscription_plan_id TEXT,
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'inactive',
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_vendors_subscription_status ON public.vendors(subscription_status);
CREATE INDEX IF NOT EXISTS idx_vendors_subscription_plan_id ON public.vendors(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_vendors_stripe_subscription_id ON public.vendors(stripe_subscription_id);
