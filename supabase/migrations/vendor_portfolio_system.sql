-- Vendor Portfolio Management System
-- This migration adds tables for vendor portfolio, availability, and location management

-- Create vendor_portfolio table for images and videos
CREATE TABLE public.vendor_portfolio (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
    media_type TEXT NOT NULL CHECK (media_type IN ('image', 'video')),
    media_url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    tags TEXT[]
);

-- Create vendor_availability table for date ranges
CREATE TABLE public.vendor_availability (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    location_id UUID REFERENCES public.vendor_locations(id),
    status TEXT NOT NULL CHECK (status IN ('available', 'booked', 'blocked')) DEFAULT 'available',
    notes TEXT,
    price_per_day NUMERIC,
    minimum_booking_days INTEGER DEFAULT 1
);

-- Create vendor_locations table for service locations
CREATE TABLE public.vendor_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    vendor_id UUID REFERENCES public.vendors(id) NOT NULL,
    location_name TEXT NOT NULL,
    address TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'USA',
    zip_code TEXT,
    latitude NUMERIC,
    longitude NUMERIC,
    service_radius INTEGER DEFAULT 50, -- miles
    is_primary BOOLEAN DEFAULT FALSE,
    travel_fee NUMERIC DEFAULT 0,
    notes TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_vendor_portfolio_vendor_id ON public.vendor_portfolio(vendor_id);
CREATE INDEX idx_vendor_portfolio_media_type ON public.vendor_portfolio(media_type);
CREATE INDEX idx_vendor_portfolio_featured ON public.vendor_portfolio(is_featured);
CREATE INDEX idx_vendor_portfolio_display_order ON public.vendor_portfolio(display_order);

CREATE INDEX idx_vendor_availability_vendor_id ON public.vendor_availability(vendor_id);
CREATE INDEX idx_vendor_availability_dates ON public.vendor_availability(start_date, end_date);
CREATE INDEX idx_vendor_availability_status ON public.vendor_availability(status);

CREATE INDEX idx_vendor_locations_vendor_id ON public.vendor_locations(vendor_id);
CREATE INDEX idx_vendor_locations_city_state ON public.vendor_locations(city, state);
CREATE INDEX idx_vendor_locations_primary ON public.vendor_locations(is_primary);

-- Enable RLS on new tables
ALTER TABLE public.vendor_portfolio ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vendor_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vendor_locations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for vendor_portfolio
CREATE POLICY "Portfolio items are viewable by everyone"
    ON public.vendor_portfolio FOR SELECT
    USING (true);

CREATE POLICY "Vendors can manage their own portfolio"
    ON public.vendor_portfolio FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.vendors v
            WHERE v.id = vendor_portfolio.vendor_id
            AND v.user_id = auth.uid()
        )
    );

-- RLS Policies for vendor_availability
CREATE POLICY "Availability is viewable by everyone"
    ON public.vendor_availability FOR SELECT
    USING (true);

CREATE POLICY "Vendors can manage their own availability"
    ON public.vendor_availability FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.vendors v
            WHERE v.id = vendor_availability.vendor_id
            AND v.user_id = auth.uid()
        )
    );

-- RLS Policies for vendor_locations
CREATE POLICY "Locations are viewable by everyone"
    ON public.vendor_locations FOR SELECT
    USING (true);

CREATE POLICY "Vendors can manage their own locations"
    ON public.vendor_locations FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.vendors v
            WHERE v.id = vendor_locations.vendor_id
            AND v.user_id = auth.uid()
        )
    );

-- Add triggers for updated_at
CREATE TRIGGER update_vendor_portfolio_modtime
    BEFORE UPDATE ON public.vendor_portfolio
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_vendor_availability_modtime
    BEFORE UPDATE ON public.vendor_availability
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_vendor_locations_modtime
    BEFORE UPDATE ON public.vendor_locations
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Add constraints to ensure data integrity
ALTER TABLE public.vendor_availability 
ADD CONSTRAINT check_date_range CHECK (end_date >= start_date);

ALTER TABLE public.vendor_portfolio 
ADD CONSTRAINT check_display_order CHECK (display_order >= 0);

ALTER TABLE public.vendor_locations 
ADD CONSTRAINT check_service_radius CHECK (service_radius >= 0);

-- Function to get vendor portfolio with media counts
CREATE OR REPLACE FUNCTION get_vendor_portfolio_summary(vendor_uuid UUID)
RETURNS TABLE (
    vendor_id UUID,
    total_images INTEGER,
    total_videos INTEGER,
    featured_items INTEGER,
    latest_update TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vp.vendor_id,
        COUNT(CASE WHEN vp.media_type = 'image' THEN 1 END)::INTEGER as total_images,
        COUNT(CASE WHEN vp.media_type = 'video' THEN 1 END)::INTEGER as total_videos,
        COUNT(CASE WHEN vp.is_featured = true THEN 1 END)::INTEGER as featured_items,
        MAX(vp.updated_at) as latest_update
    FROM public.vendor_portfolio vp
    WHERE vp.vendor_id = vendor_uuid
    GROUP BY vp.vendor_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check vendor availability for date range
CREATE OR REPLACE FUNCTION check_vendor_availability(
    vendor_uuid UUID,
    check_start_date DATE,
    check_end_date DATE
)
RETURNS TABLE (
    is_available BOOLEAN,
    conflicting_bookings INTEGER,
    available_locations UUID[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (COUNT(CASE WHEN va.status IN ('booked', 'blocked') THEN 1 END) = 0) as is_available,
        COUNT(CASE WHEN va.status IN ('booked', 'blocked') THEN 1 END)::INTEGER as conflicting_bookings,
        ARRAY_AGG(DISTINCT va.location_id) FILTER (WHERE va.status = 'available') as available_locations
    FROM public.vendor_availability va
    WHERE va.vendor_id = vendor_uuid
    AND (
        (va.start_date <= check_end_date AND va.end_date >= check_start_date)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
